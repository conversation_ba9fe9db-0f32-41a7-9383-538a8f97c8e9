# aqsoc-main 威胁通报功能实现机制深度分析报告

## 1. 功能概述

### 1.1 业务背景
威胁通报功能是aqsoc-main安全运营平台的核心业务模块，主要用于处理安全威胁事件的通报、流转和处置。该功能通过工作流引擎实现威胁事件的标准化处理流程，确保安全事件能够及时、有序地得到响应和处置。

### 1.2 核心功能
- **威胁通报列表管理**：展示待处理、处理中、已完成的威胁通报工单
- **工作流程控制**：基于JNPF工作流引擎的流程自动化处理
- **统计分析**：提供各类威胁事件的统计数据和处理状态分析
- **多系统集成**：通过代理机制实现与工作流引擎的无缝集成

### 1.3 技术特点
- **微服务架构**：aqsoc-main主平台 + aq-jnpf工作流引擎分离部署
- **代理集成**：使用smiley-http-proxy-servlet实现跨服务调用
- **数据分离**：业务数据与工作流数据分库存储
- **状态同步**：双向数据同步确保业务状态一致性

## 2. 整体架构

### 2.1 系统架构图
```mermaid
graph TB
    subgraph "前端层"
        A[Vue.js前端应用<br/>ruoyi-ui]
        A1[威胁通报页面<br/>workbench.vue]
        A2[工单管理页面<br/>work/order]
    end
    
    subgraph "aqsoc-main 主平台"
        B[aqsoc-admin<br/>Spring Boot应用]
        B1[ProxyServletConfiguration<br/>代理配置]
        B2[TblWorkOrderController<br/>工单控制器]
        B3[TblWorkOrderService<br/>工单服务]
        B4[TblWorkOrderMapper<br/>数据访问层]
    end
    
    subgraph "代理层"
        C[smiley-http-proxy-servlet<br/>HTTP代理]
        C1[URL映射: /proxy/*]
        C2[目标地址: ***************:30001]
    end
    
    subgraph "aq-jnpf 工作流引擎"
        D[jnpf-admin<br/>工作流应用]
        D1[FlowTemplateController<br/>流程模板控制器]
        D2[FlowTaskController<br/>流程任务控制器]
        D3[FlowEngineService<br/>流程引擎服务]
    end
    
    subgraph "数据层"
        E[(aqsoc数据库)]
        E1[tbl_work_order<br/>工单主表]
        E2[tbl_work_backlog<br/>待办表]
        E3[tbl_work_history<br/>历史表]
        
        F[(low_code_jnpf数据库)]
        F1[flow_engine<br/>流程引擎表]
        F2[flow_task<br/>流程任务表]
        F3[flow_template<br/>流程模板表]
    end
```

### 2.2 架构层次说明

#### 2.2.1 前端层
- **技术栈**：Vue.js + Element UI
- **主要页面**：
  - `workbench.vue`：首页工作台，展示威胁通报概览
  - `work/order`：工单管理页面，提供详细的工单操作功能
- **API调用**：通过axios发起HTTP请求到后端接口

#### 2.2.2 应用层
- **aqsoc-admin**：主业务应用，处理威胁通报的核心业务逻辑
- **代理配置**：ProxyServletConfiguration配置HTTP代理
- **控制器层**：TblWorkOrderController处理前端请求
- **服务层**：TblWorkOrderService实现业务逻辑
- **数据访问层**：TblWorkOrderMapper执行数据库操作

#### 2.2.3 代理层
- **smiley-http-proxy-servlet**：开源HTTP代理组件
- **URL映射**：将`/proxy/*`请求转发到工作流引擎
- **透明代理**：保持请求头、Cookie等信息不变

#### 2.2.4 工作流引擎层
- **aq-jnpf**：独立的工作流引擎应用
- **流程管理**：FlowTemplateController管理流程模板
- **任务处理**：FlowTaskController处理流程任务
- **引擎服务**：FlowEngineService提供核心流程能力

#### 2.2.5 数据层
- **aqsoc数据库**：存储业务数据（工单、待办、历史）
- **low_code_jnpf数据库**：存储工作流数据（流程、任务、模板）

## 3. 代理机制详解

### 3.1 代理配置实现

#### 3.1.1 配置类分析
```java
@Configuration
public class ProxyServletConfiguration {
    @Value("${jnpf.proxy.servlet_url}")
    private String servlet_url; // /proxy/*
    
    @Value("${jnpf.proxy.target_url}")
    private String target_url; // http://***************:30001
    
    @Bean
    public ServletRegistrationBean proxyServletRegistration() {
        ServletRegistrationBean registrationBean = 
            new ServletRegistrationBean(createProxyServlet(), servlet_url);
        
        Map<String, String> params = MapUtil.builder(new HashMap<String, String>())
            .put(ProxyServlet.P_TARGET_URI, target_url)        // 目标URI
            .put(ProxyServlet.P_HANDLEREDIRECTS, "false")      // 重定向处理
            .put(ProxyServlet.P_PRESERVECOOKIES, "true")       // 保持Cookie
            .put(ProxyServlet.P_PRESERVEHOST, "true")          // 保持Host
            .put(ProxyServlet.P_LOG, "true")                   // 启用日志
            .build();
        
        registrationBean.setInitParameters(params);
        return registrationBean;
    }
}
```

#### 3.1.2 配置参数说明
- **P_TARGET_URI**：代理目标地址，指向aq-jnpf工作流引擎
- **P_HANDLEREDIRECTS**：设为false，由客户端处理重定向
- **P_PRESERVECOOKIES**：保持Cookie不变，确保会话状态
- **P_PRESERVEHOST**：保持Host头不变，避免跨域问题
- **P_LOG**：启用代理日志，便于调试和监控

### 3.2 URL映射规则

#### 3.2.1 映射机制
```
前端请求：/prod-api/proxy/api/workflow/Engine/flowTemplate/ListAll
↓
代理处理：移除 /proxy 前缀
↓
转发请求：http://***************:30001/api/workflow/Engine/flowTemplate/ListAll
```

#### 3.2.2 请求转发流程
1. **请求拦截**：Spring Boot拦截匹配`/proxy/*`的请求
2. **URL重写**：移除`/proxy`前缀，保留后续路径
3. **请求转发**：将重写后的URL转发到目标服务器
4. **响应返回**：将目标服务器的响应原样返回给客户端

### 3.3 代理优势分析

#### 3.3.1 技术优势
- **透明集成**：前端无需感知后端服务的具体部署位置
- **统一入口**：所有请求通过统一的API网关进行路由
- **配置灵活**：可通过配置文件动态调整目标服务地址
- **会话保持**：自动处理Cookie和会话状态

#### 3.3.2 架构优势
- **服务解耦**：主业务系统与工作流引擎独立部署
- **扩展性强**：可以轻松添加新的代理规则和目标服务
- **维护简单**：代理配置集中管理，便于运维
- **性能优化**：减少跨域请求的复杂性

## 4. 关键代码分析

### 4.1 工单控制器核心方法

#### 4.1.1 待办列表接口
```java
@GetMapping("/waitList")
public TableDataInfo list(TblWorkOrder tblWorkOrder) {
    startPage();  // 启动分页
    List<TblWorkOrder> list = tblWorkOrderService.selectWaitList(tblWorkOrder);
    return getDataTable(list);  // 返回分页数据
}
```

**功能说明**：
- 接收前端查询参数，包括分页信息和过滤条件
- 调用服务层方法查询待办工单列表
- 返回标准的分页数据格式

#### 4.1.2 统计数据接口
```java
@GetMapping("/getStatistics")
public AjaxResult getStatistics(TblWorkOrder workOrder){
    return AjaxResult.success(tblWorkOrderService.getStatisticsData(workOrder));
}
```

**功能说明**：
- 获取工单统计数据，包括各状态的工单数量
- 支持按部门、用户等维度进行统计
- 返回JSON格式的统计结果

### 4.2 服务层业务逻辑

#### 4.2.1 待办列表查询
```java
@Override
public List<TblWorkOrder> selectWaitList(TblWorkOrder tblWorkOrder) {
    handlePrem(tblWorkOrder);  // 处理权限过滤
    List<TblWorkOrder> tblWorkOrders = tblWorkOrderMapper.selectWaitList(tblWorkOrder);
    
    for (TblWorkOrder workOrder : tblWorkOrders) {
        getFileUrls(workOrder);  // 处理附件URL
    }
    
    // 处理历史节点属性
    if(CollUtil.isNotEmpty(tblWorkOrders)){
        tblWorkOrders.forEach(workOrder -> {
            List<String> historyNodeProperties = workOrder.getHistoryNodeProperties();
            // ... 节点属性处理逻辑
        });
    }
    
    return tblWorkOrders;
}
```

**业务逻辑**：
1. **权限处理**：根据用户权限过滤可见的工单
2. **数据查询**：执行复杂的关联查询获取工单数据
3. **附件处理**：处理工单相关的附件URL
4. **节点属性**：处理工作流节点的历史属性信息

#### 4.2.2 统计数据计算
```java
@Override
public JSONObject getStatisticsData(TblWorkOrder workOrder) {
    handlePrem(workOrder);  // 权限处理
    JSONObject statisticsData = tblWorkOrderMapper.getStatisticsData(workOrder);
    
    int allCount = 0;
    for (String key : statisticsData.keySet()) {
        allCount += statisticsData.getIntValue(key,0);
    }
    statisticsData.put("allCount",allCount);  // 计算总数
    
    return statisticsData;
}
```

**统计逻辑**：
1. **权限过滤**：确保统计数据符合用户权限范围
2. **数据聚合**：执行SQL聚合查询获取各状态统计
3. **总数计算**：计算所有状态的工单总数
4. **结果封装**：返回包含详细统计信息的JSON对象

## 5. 接口调用流程

### 5.1 威胁通报列表查询流程
```mermaid
sequenceDiagram
    participant F as 前端Vue.js
    participant AC as aqsoc-admin Controller
    participant AS as aqsoc-admin Service
    participant PS as ProxyServlet
    participant JC as jnpf Controller
    participant JS as jnpf Service
    participant DB1 as aqsoc数据库
    participant DB2 as jnpf数据库
    
    Note over F,DB2: 威胁通报列表查询流程
    
    F->>+AC: GET /work/order/waitList
    Note right of F: 查询参数：pageNum, pageSize, queryState等
    
    AC->>+AS: selectWaitList(tblWorkOrder)
    AS->>+DB1: 执行复杂SQL查询
    Note right of AS: 关联查询：tbl_work_order, tbl_work_backlog, tbl_work_history, sys_dept, sys_user
    
    DB1-->>-AS: 返回工单列表数据
    AS->>AS: 处理附件URL、节点属性等业务逻辑
    AS-->>-AC: 返回处理后的工单列表
    AC-->>-F: 返回分页数据 TableDataInfo
```

### 5.2 流程模板获取流程
```mermaid
sequenceDiagram
    participant F as 前端Vue.js
    participant AC as aqsoc-admin Controller
    participant PS as ProxyServlet
    participant JC as jnpf Controller
    participant JS as jnpf Service
    participant DB2 as jnpf数据库
    
    Note over F,DB2: 流程模板获取流程
    
    F->>+AC: GET /proxy/api/workflow/Engine/flowTemplate/ListAll
    AC->>+PS: 代理转发请求
    Note right of PS: URL重写：/proxy/* → http://***************:30001/*
    
    PS->>+JC: GET /api/workflow/Engine/flowTemplate/ListAll
    JC->>+JS: flowTemplateService.getTreeList()
    JS->>+DB2: 查询flow_engine表
    DB2-->>-JS: 返回流程模板数据
    JS-->>-JC: 返回FlowTemplateListVO列表
    JC-->>-PS: 返回ActionResult<ListVO>
    PS-->>-AC: 代理返回响应
    AC-->>-F: 返回流程模板列表
```

### 5.3 关键接口说明

#### 5.3.1 主要接口列表
| 接口路径 | 方法 | 功能说明 | 数据源 |
|---------|------|----------|--------|
| `/work/order/waitList` | GET | 获取待办工单列表 | aqsoc数据库 |
| `/work/order/getStatistics` | GET | 获取工单统计数据 | aqsoc数据库 |
| `/proxy/api/workflow/Engine/flowTemplate/ListAll` | GET | 获取流程模板列表 | jnpf数据库 |
| `/proxy/api/workflow/Engine/FlowBefore/List` | GET | 获取待审批任务 | jnpf数据库 |

#### 5.3.2 接口参数说明
**waitList接口参数**：
- `pageNum`：页码
- `pageSize`：每页大小
- `queryState`：查询状态（-1:待发起, 1:待审核, 2:待处置, 3:待核验, 4:已完成）
- `handleDept`：处理部门ID
- `workName`：工单名称（模糊查询）
- `workType`：工单类型

**getStatistics接口参数**：
- `handleDept`：处理部门ID
- `createBy`：创建人ID
- `queryAll`：是否查询全部数据

## 6. 数据库设计

### 6.1 aqsoc数据库表结构

#### 6.1.1 tbl_work_order（工单主表）
| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| id | bigint | 主键ID | 自增 |
| work_no | varchar(50) | 工单编号 | 唯一标识 |
| work_name | varchar(200) | 工单名称 | 业务标题 |
| work_type | varchar(10) | 工单类型 | 0:漏洞 1:威胁 |
| event_ids | text | 关联事件ID | JSON数组 |
| application_id | varchar(50) | 对应系统ID | 关联业务系统 |
| handle_dept | bigint | 处理部门 | 外键关联sys_dept |
| handle_user | bigint | 处理用户 | 外键关联sys_user |
| expect_complete_time | datetime | 计划完成时间 | 业务时限 |
| flow_state | varchar(10) | 流程状态 | -1:待发起 0,2:待审核 1:待处置 3:待核验 4:已完成 |
| prod_id | varchar(50) | 工作流实例ID | 关联jnpf流程 |
| complete_time | datetime | 实际完成时间 | 完成时间戳 |
| remark2 | text | 节点属性 | 工作流节点配置 |
| remark5 | varchar(50) | 创建人ID | 业务创建人 |

#### 6.1.2 tbl_work_backlog（待办表）
| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| id | bigint | 主键ID | 自增 |
| work_id | bigint | 工单ID | 外键关联tbl_work_order |
| handle_user | bigint | 处理人 | 待办任务分配人 |
| is_completion | int | 是否完成 | 0:未完成 1:已完成 |
| completion_time | datetime | 完成时间 | 任务完成时间 |
| node_id | varchar(50) | 所属节点 | 工作流节点ID |
| flow_state | varchar(10) | 流程状态 | 当前流程状态 |

#### 6.1.3 tbl_work_history（历史表）
| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| id | bigint | 主键ID | 自增 |
| work_id | bigint | 工单ID | 外键关联tbl_work_order |
| handle_state | varchar(10) | 处理状态 | 历史状态记录 |
| handle_user | varchar(50) | 处理用户 | 操作人员 |
| node_properties | text | 节点配置 | 节点属性JSON |
| node_code | varchar(50) | 当前节点 | 节点编码 |
| type | int | 类型 | 1:普通 2:抄送 |
| create_time | datetime | 创建时间 | 操作时间 |

### 6.2 low_code_jnpf数据库表结构

#### 6.2.1 flow_engine（流程引擎表）
| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| F_Id | varchar(50) | 主键ID | UUID |
| F_EnCode | varchar(50) | 流程编码 | 唯一编码 |
| F_FullName | varchar(200) | 流程名称 | 显示名称 |
| F_Type | int | 流程类型 | 流程分类 |
| F_Category | varchar(50) | 流程分类 | 业务分类 |
| F_Form | varchar(50) | 流程表单 | 关联表单ID |
| F_FlowTemplateJson | longtext | 流程模板 | 流程定义JSON |
| F_FormTemplateJson | longtext | 表单模板 | 表单定义JSON |
| F_EnabledMark | int | 有效标志 | 0:禁用 1:启用 |

#### 6.2.2 flow_task（流程任务表）
| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| F_Id | varchar(50) | 主键ID | UUID |
| F_ProcessId | varchar(50) | 流程实例ID | 流程实例标识 |
| F_EnCode | varchar(50) | 任务编码 | 任务唯一编码 |
| F_FullName | varchar(200) | 任务名称 | 任务显示名称 |
| F_FlowId | varchar(50) | 流程ID | 关联flow_engine |
| F_Status | int | 任务状态 | 0:待处理 1:已处理 2:已撤回 |
| F_StartTime | datetime | 开始时间 | 任务开始时间 |
| F_EndTime | datetime | 结束时间 | 任务完成时间 |

### 6.3 数据关联关系

#### 6.3.1 跨库关联
```
aqsoc.tbl_work_order.prod_id ←→ low_code_jnpf.flow_task.F_ProcessId
```
通过流程实例ID建立业务工单与工作流任务的关联关系。

#### 6.3.2 状态映射
| aqsoc.flow_state | 说明 | jnpf.F_Status | 说明 |
|------------------|------|---------------|------|
| -1 | 待发起 | - | 未创建流程 |
| 0,2 | 待审核 | 0 | 待处理 |
| 1 | 待处置 | 0 | 待处理 |
| 3 | 待核验 | 0 | 待处理 |
| 4 | 已完成 | 1 | 已处理 |

## 7. 配置说明

### 7.1 代理配置
```yaml
# application.yml
jnpf:
  proxy:
    servlet_url: /proxy/*                    # 代理URL模式
    target_url: http://***************:30001 # 目标服务地址
```

### 7.2 数据库配置
```yaml
# aqsoc数据库配置
spring:
  datasource:
    primary:
      url: *********************************
      username: root
      password: password
      
# jnpf数据库配置（通过代理访问）
jnpf:
  datasource:
    url: jdbc:mysql://***************:3306/low_code_jnpf
    username: jnpf
    password: jnpf123
```

### 7.3 Maven依赖
```xml
<!-- 代理组件 -->
<dependency>
    <groupId>org.mitre.dsmiley.httpproxy</groupId>
    <artifactId>smiley-http-proxy-servlet</artifactId>
    <version>1.12.1</version>
</dependency>
```

## 8. 问题分析

### 8.1 现有问题

#### 8.1.1 性能问题
1. **代理延迟**：每次工作流接口调用都需要经过HTTP代理，增加了网络延迟
2. **数据库查询复杂**：waitList接口的SQL查询涉及多表关联，性能较差
3. **重复查询**：统计接口和列表接口可能存在重复的数据查询

#### 8.1.2 可靠性问题
1. **单点故障**：工作流引擎服务不可用时，整个威胁通报功能受影响
2. **数据一致性**：跨库数据同步可能存在延迟或不一致
3. **事务处理**：跨系统操作缺乏分布式事务保障

#### 8.1.3 维护问题
1. **配置复杂**：需要维护两套独立的应用和数据库
2. **调试困难**：跨系统调用的问题定位和调试比较复杂
3. **版本兼容**：两个系统的版本升级需要考虑兼容性

### 8.2 潜在风险

#### 8.2.1 安全风险
1. **代理安全**：HTTP代理可能成为安全攻击的入口点
2. **数据泄露**：跨网络传输的数据存在泄露风险
3. **权限控制**：跨系统的权限控制可能存在漏洞

#### 8.2.2 扩展性风险
1. **性能瓶颈**：随着数据量增长，现有架构可能成为性能瓶颈
2. **功能扩展**：新增功能需要同时修改两个系统
3. **技术债务**：代理机制增加了系统的复杂性

## 9. 优化建议

### 9.1 短期优化

#### 9.1.1 性能优化
1. **SQL优化**：
   - 为waitList查询添加合适的数据库索引
   - 优化复杂关联查询，考虑分步查询
   - 实现查询结果缓存机制

2. **接口优化**：
   - 实现接口响应缓存
   - 添加接口限流和熔断机制
   - 优化数据传输格式，减少网络开销

3. **代理优化**：
   - 配置连接池，提高代理性能
   - 启用HTTP/2协议，减少连接开销
   - 实现代理请求的负载均衡

#### 9.1.2 可靠性优化
1. **监控告警**：
   - 添加接口调用监控和告警
   - 实现数据库连接监控
   - 配置系统健康检查

2. **容错处理**：
   - 实现接口调用重试机制
   - 添加降级处理逻辑
   - 配置熔断器防止雪崩

3. **数据同步**：
   - 实现数据同步状态监控
   - 添加数据一致性校验
   - 配置数据修复机制

### 9.2 中期优化

#### 9.2.1 架构优化
1. **服务网格**：
   - 引入Istio等服务网格技术
   - 实现更好的服务治理和监控
   - 提供统一的安全策略

2. **消息队列**：
   - 使用MQ实现异步数据同步
   - 减少系统间的直接依赖
   - 提高系统的可扩展性

3. **API网关**：
   - 替换简单的HTTP代理
   - 提供统一的API管理
   - 实现更好的安全控制

#### 9.2.2 数据优化
1. **数据库优化**：
   - 考虑读写分离架构
   - 实现数据分片策略
   - 优化数据库连接池配置

2. **缓存策略**：
   - 引入Redis等缓存中间件
   - 实现多级缓存架构
   - 配置缓存失效策略

### 9.3 长期优化

#### 9.3.1 微服务化
1. **服务拆分**：
   - 将威胁通报功能独立为微服务
   - 实现更细粒度的服务划分
   - 提高系统的可维护性

2. **容器化部署**：
   - 使用Docker容器化部署
   - 引入Kubernetes进行容器编排
   - 实现自动化运维

3. **云原生架构**：
   - 迁移到云原生架构
   - 利用云服务提供的能力
   - 实现更好的弹性伸缩

#### 9.3.2 技术升级
1. **框架升级**：
   - 升级Spring Boot到最新版本
   - 引入Spring Cloud微服务框架
   - 使用更现代的技术栈

2. **工作流引擎**：
   - 考虑使用更轻量级的工作流引擎
   - 实现工作流引擎的云原生化
   - 提供更好的可视化能力

## 10. 威胁通报工作流完整生命周期详解

### 10.1 生命周期概览

威胁通报工作流从创建到完成经历以下关键阶段：

```mermaid
stateDiagram-v2
    [*] --> 草稿状态
    草稿状态 --> 待发起: 保存草稿
    待发起 --> 待审核: 提交流程
    待审核 --> 待处置: 审核通过
    待审核 --> 待发起: 驳回到发起人
    待处置 --> 待核验: 处置完成
    待核验 --> 已完成: 核验通过
    待核验 --> 待处置: 核验不通过

    待审核 --> 转办中: 转办操作
    转办中 --> 待审核: 转办完成

    待发起 --> 撤回: 撤回操作
    待审核 --> 撤回: 撤回操作
    待处置 --> 撤回: 撤回操作

    已完成 --> [*]
    撤回 --> [*]
```

### 10.2 详细生命周期流程

#### 10.2.1 阶段一：工单创建与草稿保存

**触发条件**：用户在前端创建新的威胁通报工单

**核心代码流程**：

<augment_code_snippet path="aqsoc-monitor/src/main/java/com/ruoyi/work/controller/TblWorkOrderController.java" mode="EXCERPT">
````java
/**
 * 新增工单主表
 */
@Log(title = "工单主表", businessType = BusinessType.INSERT)
@PostMapping("/{processId}")
public AjaxResult add(@PathVariable("processId") String processId, @RequestBody TblWorkOrder tblWorkOrder) {
    // 加入流程实例id
    logger.info("新增流程：{}，Form：{}",processId,tblWorkOrder);
    //todo 暂存 可能没有用户 那就是发起人处理
    tblWorkOrder.setProdId(processId);
    return toAjax(tblWorkOrderService.insertTblWorkOrder(tblWorkOrder));
}
````
</augment_code_snippet>

<augment_code_snippet path="aqsoc-monitor/src/main/java/com/ruoyi/work/service/impl/TblWorkOrderServiceImpl.java" mode="EXCERPT">
````java
/**
 * 新增工单主表
 */
@Transactional(rollbackFor = Exception.class)
@Override
public int insertTblWorkOrder(TblWorkOrder tblWorkOrder) {
    Long workID = snowflake.nextId();
    if(StrUtil.isBlank(tblWorkOrder.getWorkName())){
        return 1;
    }

    // 设置工单基本信息
    tblWorkOrder.setId(workID);
    tblWorkOrder.setCreateTime(DateUtils.getNowDate());
    tblWorkOrder.setCreateBy(getUsername());

    // 生成工单编号
    String workNo = generateWorkNo();
    tblWorkOrder.setWorkNo(workNo);

    // 设置默认状态为待发起
    if(tblWorkOrder.getFlowState() == null){
        tblWorkOrder.setFlowState("-1"); // 待发起状态
    }

    //保存通报信息
    int i = tblWorkOrderMapper.insertTblWorkOrder(tblWorkOrder);
    //保存通报对象信息
    List<JSONObject> reportTargetForm = tblWorkOrder.getReportTargetForm();
    handleReportTargetForm(reportTargetForm,tblWorkOrder);

    return i;
}
````
</augment_code_snippet>

<augment_code_snippet path="jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowDynamicServiceImpl.java" mode="EXCERPT">
````java
@Override
@DSTransactional
public void flowTask(FlowModel flowModel, FlowStatusEnum flowStatus, String formId) throws WorkFlowException {
    //流程数据
    switch (flowStatus) {
        case save:
            FlowTaskEntity taskEntity = flowTaskNewService.save(flowModel);
            JSONObject resData = flowModel.getResData();
            resData.put("flowTask",taskEntity);
            flowModel.setResData(resData);
            break;
        case submit:
            //todo 获取表单最新数据
            flowModel.setFormData(flowTaskUtil.infoData(formId, flowModel.getProcessId()));
            flowTaskNewService.submitAll(flowModel);
            break;
        default:
            break;
    }
}
````
</augment_code_snippet>

**状态变化**：
- **工单状态**：`flow_state = "-1"` (待发起)
- **工作流状态**：`F_Status = 0` (草稿状态)
- **数据库操作**：
  - 插入 `tbl_work_order` 记录
  - 创建 `flow_task` 实例
  - 生成流程实例ID (`prod_id`)

#### 10.2.2 阶段二：流程正式提交

**触发条件**：用户点击"提交"按钮，正式发起工作流

**核心代码流程**：

<augment_code_snippet path="jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java" mode="EXCERPT">
````java
@Override
@DSTransactional
public void submit(FlowModel flowModel) throws WorkFlowException {
    UserInfo userInfo = flowModel.getUserInfo();
    flowModel.setStatus(FlowStatusEnum.submit.getMessage());
    //流程节点
    List<FlowTaskNodeEntity> taskNodeList = new ArrayList<>();
    List<ChildNodeList> nodeListAll = new ArrayList<>();
    //流程经办
    List<FlowTaskOperatorEntity> operatorList = new ArrayList<>();
    FlowTaskEntity flowTask = save(flowModel);
    boolean isRejectId = StringUtil.isEmpty(flowTask.getRejectId());

    if (isRejectId) {
        //发起用户信息
        flowTaskUtil.flowUser(flowTask);
        flowTask.setStartTime(new Date());
        flowModel.setTaskOperatorId(FlowNature.ParentId);

        // 处理开始节点
        ChildNodeList start = flowTaskUtil.startNode(flowModel.getFlowId());

        // 创建开始节点记录
        FlowTaskNodeEntity startNode = new FlowTaskNodeEntity();
        startNode.setTaskId(flowTask.getId());
        startNode.setNodeCode(start.getCustom().getNodeId());
        startNode.setNodeName(start.getProperties().getTitle());
        startNode.setNodeType(start.getType());
        startNode.setCompletion(FlowNature.AuditCompletion);
        startNode.setState(FlowNodeEnum.Process.getCode());
        taskNodeList.add(startNode);

        // 流程流转到下一节点
        flowTaskUtil.nextNode(flowTask, start, operatorList, taskNodeList, flowModel);
    }

    // 更新流程状态
    flowTask.setStatus(FlowTaskStatusEnum.Handle.getCode());
    flowTask.setRejectId(null);
    flowTaskService.update(flowTask);

    if (isRejectId) {
        //获取抄送人
        List<FlowTaskCirculateEntity> circulateList = new ArrayList<>();
        flowTaskUtil.circulateList(start, circulateList, flowModel, flowTask);
        flowTaskCirculateService.create(circulateList);

        //自动审批
        FlowApproveModel approveModel = FlowApproveModel.builder()
            .operatorList(operatorList)
            .taskNodeList(taskNodeList)
            .flowTask(flowTask)
            .flowModel(flowModel)
            .isSubmit(true)
            .build();
        flowTaskUtil.approve(approveModel);
    }
}
````
</augment_code_snippet>

**状态变化**：
- **工单状态**：`flow_state = "2"` (待审核)
- **工作流状态**：`F_Status = 0` (待处理)
- **数据库操作**：
  - 更新 `tbl_work_order.flow_state`
  - 创建 `flow_task_node` 节点记录
  - 创建 `flow_task_operator` 经办人记录
  - 创建 `flow_task_circulate` 抄送记录
  - 插入 `tbl_work_backlog` 待办任务

**抄送机制**：

<augment_code_snippet path="jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/util/FlowTaskUtil.java" mode="EXCERPT">
````java
// 抄送人处理逻辑
public void circulateList(ChildNodeList nodeModel, List<FlowTaskCirculateEntity> circulateList,
                         FlowModel flowModel, FlowTaskEntity flowTask) {
    Properties properties = nodeModel.getProperties();
    List<String> userIdAll = new ArrayList<>();
    userIdAll.addAll(properties.getCirculateUser());  // 直接指定用户
    userIdAll.addAll(properties.getCirculateRole());  // 角色用户
    userIdAll.addAll(properties.getCirculatePosition()); // 职位用户
    userIdAll.addAll(properties.getCirculateGroup());    // 用户组
    userIdAll.addAll(properties.getCirculateOrg());      // 组织用户

    // 生成抄送实体
    for (String userId : userIdAll) {
        FlowTaskCirculateEntity entity = new FlowTaskCirculateEntity();
        entity.setTaskId(flowTask.getId());
        entity.setCirculateUserId(userId);
        entity.setCreatorTime(new Date());
        circulateList.add(entity);
    }
}
````
</augment_code_snippet>

#### 10.2.3 阶段三：审核节点处理

**触发条件**：审核人员对待审核的工单进行审批操作

**审核通过流程**：

<augment_code_snippet path="jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java" mode="EXCERPT">
````java
@Override
@DSTransactional
public void audit(FlowTaskEntity flowTask, FlowTaskOperatorEntity operator, FlowModel flowModel) throws WorkFlowException {
    UserInfo userInfo = flowModel.getUserInfo();
    //判断是否审批过
    if (!FlowNature.ProcessCompletion.equals(operator.getCompletion())) {
        throw new WorkFlowException(MsgCode.WF005.get());
    }
    //判断流程是否处于挂起状态
    flowTaskUtil.isSuspend(flowTask);

    // 更新经办人状态
    operator.setCompletion(FlowNature.AuditCompletion);
    operator.setHandleTime(new Date());
    operator.setHandleOpinion(flowModel.getHandleOpinion());
    flowTaskOperatorService.update(operator);

    // 创建审批记录
    FlowTaskOperatorRecordEntity operatorRecord = new FlowTaskOperatorRecordEntity();
    operatorRecord.setTaskId(flowTask.getId());
    operatorRecord.setHandleId(operator.getHandleId());
    operatorRecord.setHandleStatus(FlowRecordEnum.audit.getCode());
    operatorRecord.setHandleOpinion(flowModel.getHandleOpinion());
    operatorRecord.setHandleTime(new Date());
    flowTaskOperatorRecordService.create(operatorRecord);

    // 获取当前节点信息
    List<FlowTaskNodeEntity> taskNodeList = flowTaskNodeService.getList(flowTask.getId());
    FlowTaskNodeEntity flowTaskNode = taskNodeList.stream()
        .filter(t -> operator.getNodeCode().equals(t.getNodeCode()))
        .findFirst().orElse(null);

    // 流程流转到下一节点
    flowTaskUtil.auditTaskNode(templateAllModel, flowTask, flowTaskNode, flowModel);
}
````
</augment_code_snippet>

**状态变化**：
- **工单状态**：`flow_state = "1"` (待处置)
- **工作流状态**：当前节点完成，流转到下一节点
- **数据库操作**：
  - 更新 `flow_task_operator.completion`
  - 插入 `flow_task_operator_record` 审批记录
  - 更新 `tbl_work_order.flow_state`
  - 更新 `tbl_work_backlog` 待办状态
  - 插入 `tbl_work_history` 历史记录

**驳回流程**：

<augment_code_snippet path="jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java" mode="EXCERPT">
````java
@Override
@DSTransactional
public void reject(FlowTaskEntity flowTask, FlowTaskOperatorEntity operator, FlowModel flowModel) throws WorkFlowException {
    UserInfo userInfo = flowModel.getUserInfo();
    String userId = StringUtil.isNotEmpty(flowModel.getUserId()) ? flowModel.getUserId() : userInfo.getUserId();
    List<String> rejectThisStepId = Arrays.asList(flowTask.getThisStepId().split("," ));

    //判断流程是否处于挂起状态
    flowTaskUtil.isSuspend(flowTask);

    // 确定驳回目标节点
    String rejectStep = flowModel.getRejectStep();
    boolean rejectType = FlowNature.PresentType.equals(flowModel.getRejectType());
    boolean isStart = FlowNature.StartType.equals(flowModel.getRejectType());

    // 创建驳回记录
    FlowTaskOperatorRecordEntity operatorRecord = new FlowTaskOperatorRecordEntity();
    operatorRecord.setTaskId(flowTask.getId());
    operatorRecord.setHandleId(operator.getHandleId());
    operatorRecord.setHandleStatus(FlowRecordEnum.reject.getCode());
    operatorRecord.setHandleOpinion(flowModel.getHandleOpinion());
    operatorRecord.setHandleTime(new Date());
    flowTaskOperatorRecordService.create(operatorRecord);

    // 流程回退处理
    if (isStart) {
        // 驳回到发起人
        flowTask.setStatus(FlowTaskStatusEnum.Reject.getCode());
        flowTask.setCompletion(FlowNature.ProcessCompletion);
        flowTask.setThisStepId(String.join(",", new ArrayList<>()));
        flowTask.setThisStep(String.join(",", new ArrayList<>()));
    } else if (rejectType) {
        // 驳回到上一节点
        flowTask.setThisStepId(rejectStep);
    }

    flowTaskService.update(flowTask);
}
````
</augment_code_snippet>

#### 10.2.4 阶段四：处置节点执行

**触发条件**：处置人员接收到待处置任务并进行处理

**处置流程**：

<augment_code_snippet path="aqsoc-monitor/src/main/java/com/ruoyi/work/service/impl/TblWorkOrderServiceImpl.java" mode="EXCERPT">
````java
/**
 * 修改工单主表
 */
@Transactional(rollbackFor = Exception.class)
@Override
public int updateTblWorkOrder(TblWorkOrder tblWorkOrder) {
    Lock lock = lockPool.computeIfAbsent(tblWorkOrder.getId() != null? tblWorkOrder.getId().toString() : tblWorkOrder.getProdId(), id -> new ReentrantLock());
    lock.lock();
    try {
        // 工单状态为 待处理变更为待验证  待验证 根据验证是否通过将状态变更为 待处理 / 处理完成
        TblWorkOrder workOrderInDB = null;
        if(tblWorkOrder.getId() != null){
            workOrderInDB = this.selectTblWorkOrderById(tblWorkOrder.getId());
        }

        // 更新工单基本信息
        tblWorkOrder.setUpdateBy(getUsername());
        tblWorkOrder.setUpdateTime(DateUtils.getNowDate());

        int updateTblWorkOrder = tblWorkOrderMapper.updateTblWorkOrder(tblWorkOrder);

        //通报对象处理
        List<JSONObject> reportTargetForm = tblWorkOrder.getReportTargetForm();
        handleReportTargetForm(reportTargetForm,tblWorkOrder);

        if(isTempSave){
            return updateTblWorkOrder;
        }

        //通知
        if(flowNotify != null && flowNotify.getBoolean("enabled") != null && flowNotify.getBoolean("enabled") && nodeIsFinished.get()){
            this.sendFlowNotify(flowInfo, notifyList,tblWorkOrder);
        }

        // 处理关联事件状态
        if(CollUtil.isNotEmpty(eventDataList)){
            //按类型分类
            for (int i = 0; i < 4; i++) {
                //获取事件
                int finalI = i;
                List<JSONObject> matchList = eventDataList.stream().filter(item -> String.valueOf(finalI + 1).equals(item.getString("type"))).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(matchList)){
                    matchList.forEach(item -> {
                        String eventId = item.getString("eventId");
                        String type = item.getString("type");
                        Integer handleState = item.getInteger("handleState");
                        String eventHandleState = "1";
                        if(handleState != null && handleState == 2){
                            eventHandleState = "2";
                        }
                        handleEvent(CollUtil.toList(item),type,eventHandleState,tblWorkOrder);
                    });
                }
            }
        }

        return updateTblWorkOrder;
    } finally {
        lock.unlock();
    }
}
````
</augment_code_snippet>

**事件处理逻辑**：

<augment_code_snippet path="aqsoc-monitor/src/main/java/com/ruoyi/work/service/impl/TblWorkOrderServiceImpl.java" mode="EXCERPT">
````java
private void handleEvent(List<JSONObject> eventDataList,String workType,String state,TblWorkOrder tblWorkOrder){
    LoginUser loginUser = SecurityUtils.getLoginUser();
    SyncMessage<Object> syncMessage = new SyncMessage<>();
    syncMessage.setOperationType(OperationTypeEnum.INSERT);
    List<Object> syncDataList = new ArrayList<>();
    List<Long> eventList = eventDataList.stream().map(item -> item.getLong("eventId")).collect(Collectors.toList());

    if("1".equals(workType)){
        //IP漏洞
        List<MonitorBssVulnDeal> handleList = eventDataList.stream().map(item -> {
            MonitorBssVulnDeal monitorBssVulnDeal = new MonitorBssVulnDeal();
            monitorBssVulnDeal.setId(item.getLong("eventId"));
            monitorBssVulnDeal.setHandleState(state);  // 1:已处理 2:误报
            monitorBssVulnDeal.setWorkOrderId(tblWorkOrder.getId());
            monitorBssVulnDeal.setHandleTime(DateUtil.date());
            monitorBssVulnDeal.setHandleUser(loginUser.getUsername());
            return monitorBssVulnDeal;
        }).collect(Collectors.toList());
        monitorBssVulnDealService.updateBatchById(handleList);
        syncDataList.addAll(handleList);
    }else if("2".equals(workType)){
        //Web漏洞 - 类似处理逻辑
    }else if("3".equals(workType)){
        //弱口令 - 类似处理逻辑
    }else if("4".equals(workType)){
        //威胁事件 - 类似处理逻辑
    }

    // 同步数据到中台
    if(CollUtil.isNotEmpty(syncDataList)){
        syncDataList.forEach(syncDataItem -> {
            syncMessage.setData(syncDataItem);
            syncMessage.setTimestamp(System.currentTimeMillis());
            handleDataSyncSender.sendDataSync(syncMessage);
        });
    }
}
````
</augment_code_snippet>

**状态变化**：
- **工单状态**：`flow_state = "3"` (待核验)
- **关联事件状态**：更新为已处理或误报
- **数据库操作**：
  - 更新 `tbl_work_order` 处置信息
  - 更新关联事件表的 `handle_state`
  - 同步数据到中台系统

#### 10.2.5 阶段五：核验节点确认

**触发条件**：核验人员对处置结果进行验证

**核验通过流程**：

<augment_code_snippet path="jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/util/FlowTaskUtil.java" mode="EXCERPT">
````java
/**
 * 流程完成处理
 */
public void flowCompletion(FlowTaskEntity flowTask, FlowModel flowModel) {
    // 判断是否为最后节点
    List<FlowTaskNodeEntity> taskNodeList = flowTaskNodeService.getList(flowTask.getId());
    boolean isLastNode = taskNodeList.stream()
        .filter(t -> FlowNature.AuditCompletion.equals(t.getCompletion()))
        .count() == taskNodeList.size() - 1; // 排除开始节点

    if (isLastNode) {
        // 流程完成
        flowTask.setStatus(FlowTaskStatusEnum.Completion.getCode());
        flowTask.setCompletion(FlowNature.AuditCompletion);
        flowTask.setEndTime(new Date());
        flowTaskService.update(flowTask);

        // 触发流程完成事件
        FlowMsgModel flowMsgModel = new FlowMsgModel();
        flowMsgModel.setTaskEntity(flowTask);
        flowMsgModel.setCompletion(true);
        flowMsgUtil.message(flowMsgModel);

        // 更新工单状态为已完成
        updateWorkOrderStatus(flowTask.getProcessId(), "4");
    }
}
````
</augment_code_snippet>

**核验不通过流程**：

<augment_code_snippet path="aqsoc-monitor/src/main/java/com/ruoyi/work/service/impl/TblWorkOrderServiceImpl.java" mode="EXCERPT">
````java
/**
 * 工单状态同步处理
 */
private void syncWorkOrderStatus(String processId, String flowState) {
    // 根据流程状态映射工单状态
    String workOrderState = flowStateMap.get(flowState);
    if (workOrderState != null) {
        TblWorkOrder updateOrder = new TblWorkOrder();
        updateOrder.setProdId(processId);
        updateOrder.setFlowState(flowState);
        updateOrder.setUpdateTime(DateUtils.getNowDate());

        // 更新工单状态
        tblWorkOrderMapper.updateByProcessId(updateOrder);

        // 更新待办任务状态
        if ("4".equals(flowState)) {
            // 已完成，清理待办
            tblWorkBacklogService.deleteByProcessId(processId);
        } else if ("1".equals(flowState)) {
            // 驳回到待处置，重新生成待办
            generateBacklogTask(processId, "disposal");
        }

        // 记录状态变更历史
        TblWorkHistory history = new TblWorkHistory();
        history.setWorkOrderId(getWorkOrderIdByProcessId(processId));
        history.setOperationType("状态变更");
        history.setOperationContent("工单状态变更为：" + workOrderState);
        history.setOperationTime(DateUtils.getNowDate());
        history.setOperationUser(getUsername());
        tblWorkHistoryService.insertTblWorkHistory(history);
    }
}
````
</augment_code_snippet>

**状态变化**：
- **核验通过**：`flow_state = "4"` (已完成)
- **核验不通过**：`flow_state = "1"` (待处置)
- **数据库操作**：
  - 更新 `tbl_work_order.flow_state`
  - 更新 `flow_task.status` 和 `end_time`
  - 清理 `tbl_work_backlog` 待办记录
  - 插入 `tbl_work_history` 完成记录

#### 10.2.6 特殊操作：转办机制

**转办流程**：

<augment_code_snippet path="jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java" mode="EXCERPT">
````java
@Override
@DSTransactional
public void transfer(FlowTaskOperatorEntity taskOperator, FlowModel flowModel) throws WorkFlowException {
    // 更新经办人信息
    taskOperator.setHandleId(flowModel.getFreeApproverUserId());
    taskOperator.setCreatorTime(new Date());
    flowTaskOperatorService.update(taskOperator);

    // 获取节点信息
    List<FlowTaskNodeEntity> taskNodeList = flowTaskNodeService.getList(taskOperator.getTaskId());
    FlowTaskNodeEntity taskNode = taskNodeList.stream()
        .filter(t -> t.getId().equals(taskOperator.getTaskNodeId()))
        .findFirst().orElse(null);
    ChildNodeList childNode = JsonUtil.getJsonToBean(taskNode.getNodePropertyJson(), ChildNodeList.class);

    // 创建转办记录
    UserInfo userInfo = flowModel.getUserInfo();
    FlowTaskOperatorRecordEntity operatorRecord = new FlowTaskOperatorRecordEntity();
    FlowTaskOperatorEntity operator = new FlowTaskOperatorEntity();
    operator.setHandleId(userInfo.getUserId());
    operator.setTaskId(taskOperator.getTaskId());
    operator.setTaskNodeId(taskOperator.getTaskNodeId());
    operator.setNodeCode(taskOperator.getNodeCode());
    operator.setNodeName(taskOperator.getNodeName());

    FlowOperatordModel flowOperatordModel = FlowOperatordModel.builder()
        .status(FlowRecordEnum.transfer.getCode())
        .flowModel(flowModel)
        .userId(userInfo.getUserId())
        .operator(operator)
        .operatorId(taskOperator.getHandleId())
        .build();
    flowTaskUtil.operatorRecord(operatorRecord, flowOperatordModel);
    flowTaskOperatorRecordService.create(operatorRecord);

    // 发送转办消息通知
    FlowMsgModel flowMsgModel = new FlowMsgModel();
    flowMsgModel.setTransfer(true);
    flowMsgModel.setTaskEntity(flowTaskService.getInfo(taskOperator.getTaskId()));
    flowMsgModel.setOperatorEntity(taskOperator);
    flowMsgUtil.message(flowMsgModel);
}
````
</augment_code_snippet>

#### 10.2.7 特殊操作：撤回机制

**撤回流程**：

<augment_code_snippet path="jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java" mode="EXCERPT">
````java
@Override
@DSTransactional
public void recall(String id, FlowTaskOperatorRecordEntity operatorRecord, FlowModel flowModel) throws WorkFlowException {
    UserInfo userInfo = flowModel.getUserInfo();

    //撤回经办
    FlowTaskOperatorEntity operatorEntity = flowTaskOperatorService.getInfo(operatorRecord.getTaskOperatorId());
    if (FlowNodeEnum.Futility.getCode().equals(operatorEntity.getState())) {
        throw new WorkFlowException(MsgCode.WF104.get());
    }

    // 获取流程信息
    FlowTaskEntity flowTask = flowTaskService.getInfo(operatorRecord.getTaskId());
    List<FlowTaskNodeEntity> flowTaskNodeEntityList = flowTaskNodeService.getList(operatorRecord.getTaskId());
    List<FlowTaskOperatorEntity> flowTaskOperatorEntityList = flowTaskOperatorService.getList(operatorRecord.getTaskId());

    FlowTaskNodeEntity flowTaskNodeEntity = flowTaskNodeEntityList.stream()
        .filter(t -> t.getId().equals(operatorRecord.getTaskNodeId()))
        .findFirst().orElse(null);

    //任务待审状态才能撤回
    if (!(flowTask.getEnabledMark() == 1 && FlowTaskStatusEnum.Handle.getCode().equals(flowTask.getStatus()))) {
        throw new WorkFlowException(MsgCode.WF105.get());
    }

    //撤回节点下一节点已操作检查
    List<FlowTaskOperatorEntity> recallNextOperatorList = flowTaskOperatorEntityList.stream()
        .filter(x -> flowTaskNodeEntity.getNodeNext().contains(x.getNodeCode()))
        .collect(Collectors.toList());
    boolean isRecall = recallNextOperatorList.stream()
        .filter(t -> FlowNature.AuditCompletion.equals(t.getCompletion()) && FlowNodeEnum.Process.getCode().equals(t.getState()))
        .count() > 0;
    if (isRecall) {
        throw new WorkFlowException(MsgCode.WF106.get());
    }

    // 撤回节点是否完成
    if (FlowNature.AuditCompletion.equals(flowTaskNodeEntity.getCompletion())) {
        //撤回节点下一节点经办删除
        List<String> idAll = recallNextOperatorList.stream()
            .map(FlowTaskOperatorEntity::getId)
            .collect(Collectors.toList());
        flowTaskOperatorService.updateTaskOperatorState(idAll);

        // 重置当前节点经办人状态
        List<FlowTaskOperatorEntity> hanleOperatorList = flowTaskOperatorEntityList.stream()
            .filter(x -> x.getTaskNodeId().equals(operatorRecord.getTaskNodeId())
                && Objects.isNull(x.getHandleStatus())
                && Objects.isNull(x.getHandleTime())
                && Objects.isNull(x.getParentId()))
            .collect(Collectors.toList());
        for (FlowTaskOperatorEntity taskOperator : hanleOperatorList) {
            taskOperator.setCompletion(FlowNature.ProcessCompletion);
        }
        flowTaskOperatorService.updateList(hanleOperatorList);
    }

    // 创建撤回记录
    FlowTaskOperatorRecordEntity revokeRecord = new FlowTaskOperatorRecordEntity();
    revokeRecord.setTaskId(operatorRecord.getTaskId());
    revokeRecord.setHandleId(userInfo.getUserId());
    revokeRecord.setHandleStatus(FlowRecordEnum.recall.getCode());
    revokeRecord.setHandleOpinion(flowModel.getHandleOpinion());
    revokeRecord.setHandleTime(new Date());
    flowTaskOperatorRecordService.create(revokeRecord);
}
````
</augment_code_snippet>

**发起撤回（revoke）流程**：

<augment_code_snippet path="jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java" mode="EXCERPT">
````java
@Override
@DSTransactional
public void revoke(FlowTaskEntity flowTask, FlowModel flowModel) throws WorkFlowException {
    //判断流程是否处于挂起状态
    flowTaskUtil.isSuspend(flowTask);
    UserInfo userInfo = flowModel.getUserInfo();

    List<FlowTaskNodeEntity> list = flowTaskNodeService.getList(flowTask.getId());
    FlowTaskNodeEntity start = list.stream()
        .filter(t -> FlowNature.NodeStart.equals(String.valueOf(t.getNodeType())))
        .findFirst().orElse(null);

    //删除节点
    flowTaskNodeService.update(flowTask.getId());
    //删除经办
    flowTaskOperatorService.update(flowTask.getId());
    //删除候选人
    flowCandidatesService.deleteByTaskId(flowTask.getId());
    //删除发起用户信息
    flowUserService.deleteByTaskId(flowTask.getId());

    //更新当前节点
    flowTask.setThisStep("开始");
    flowTask.setCompletion(FlowNature.ProcessCompletion);
    flowTask.setStatus(FlowTaskStatusEnum.Revoke.getCode());
    flowTask.setThisStepId(start.getNodeCode());
    flowTaskService.update(flowTask);

    // 创建撤回记录
    FlowTaskOperatorRecordEntity operatorRecord = new FlowTaskOperatorRecordEntity();
    operatorRecord.setTaskId(flowTask.getId());
    operatorRecord.setHandleId(userInfo.getUserId());
    operatorRecord.setHandleStatus(FlowRecordEnum.revoke.getCode());
    operatorRecord.setHandleOpinion("发起撤回");
    operatorRecord.setHandleTime(new Date());
    flowTaskOperatorRecordService.create(operatorRecord);
}
````
</augment_code_snippet>

### 10.3 状态同步机制

#### 10.3.1 状态映射关系
```java
// TblWorkOrderServiceImpl中的状态映射
private static final Map<String,String> flowStateMap = new HashMap<>();
static {
    flowStateMap.put("-1", "待发起");   // 草稿或撤回状态
    flowStateMap.put("0", "待审核");    // 审核节点
    flowStateMap.put("2", "待审核");    // 审核节点（兼容）
    flowStateMap.put("1", "待处置");    // 处置节点
    flowStateMap.put("3", "待核验");    // 核验节点
    flowStateMap.put("4", "已完成");    // 流程完成
}
```

#### 10.3.2 同步时机
1. **流程提交时**：工单状态从 `-1` 更新为 `2`
2. **审核通过时**：工单状态从 `2` 更新为 `1`
3. **处置完成时**：工单状态从 `1` 更新为 `3`
4. **核验通过时**：工单状态从 `3` 更新为 `4`
5. **驳回操作时**：根据驳回目标节点更新状态
6. **撤回操作时**：工单状态更新为 `-1`

### 10.4 通知机制

#### 10.4.1 流程通知
```java
// 流程通知处理
private void sendFlowNotify(JSONObject flowInfo, List<TblWorkBacklog> notifyList, TblWorkOrder tblWorkOrder) {
    JSONObject flowNotify = flowInfo.getJSONObject("flowNotify");
    if (flowNotify != null && flowNotify.getBoolean("enabled")) {
        // 生成系统通知
        for (TblWorkBacklog backlog : notifyList) {
            SysNotice notice = new SysNotice();
            notice.setNoticeTitle("威胁通报处理通知");
            notice.setNoticeContent("您有新的威胁通报需要处理：" + tblWorkOrder.getWorkName());
            notice.setNoticeType("1");
            notice.setStatus("0");
            notice.setCreateBy(tblWorkOrder.getCreateBy());
            notice.setCreateTime(new Date());
            sysNoticeService.insertNotice(notice);
        }

        // 发送短信通知（如果配置启用）
        if (flowNotify.getBoolean("smsEnabled")) {
            sendSmsNotification(notifyList, tblWorkOrder);
        }
    }
}
```

#### 10.4.2 抄送通知
```java
// 抄送人通知处理
FlowMsgModel flowMsgModel = new FlowMsgModel();
flowMsgModel.setCopy(true);  // 标记为抄送消息
flowMsgModel.setCirculateList(circulateList);
flowMsgModel.setTaskEntity(flowTask);
flowMsgUtil.message(flowMsgModel);
```

### 10.5 数据一致性保障

#### 10.5.1 事务管理
```java
@Transactional(rollbackFor = Exception.class)
public int updateTblWorkOrder(TblWorkOrder tblWorkOrder) {
    // 使用分布式锁保证数据一致性
    Lock lock = lockPool.computeIfAbsent(tblWorkOrder.getId().toString(), id -> new ReentrantLock());
    lock.lock();
    try {
        // 业务处理逻辑
        return processWorkOrder(tblWorkOrder);
    } finally {
        lock.unlock();
    }
}
```

#### 10.5.2 状态校验
```java
// 状态流转校验
private void validateStateTransition(String currentState, String targetState) {
    Map<String, List<String>> allowedTransitions = new HashMap<>();
    allowedTransitions.put("-1", Arrays.asList("2"));           // 待发起 -> 待审核
    allowedTransitions.put("2", Arrays.asList("1", "-1"));      // 待审核 -> 待处置/待发起
    allowedTransitions.put("1", Arrays.asList("3", "2"));       // 待处置 -> 待核验/待审核
    allowedTransitions.put("3", Arrays.asList("4", "1"));       // 待核验 -> 已完成/待处置

    if (!allowedTransitions.get(currentState).contains(targetState)) {
        throw new BusinessException("非法的状态流转：" + currentState + " -> " + targetState);
    }
}
```

## 11. 总结

### 11.1 技术总结
aqsoc-main项目的威胁通报功能通过smiley-http-proxy-servlet实现了与aq-jnpf工作流引擎的集成，采用了微服务架构和数据分离的设计思路。整体架构清晰，功能完整，能够满足当前的业务需求。

### 11.2 优势分析
1. **架构清晰**：前后端分离，业务逻辑与工作流引擎分离
2. **功能完整**：涵盖了威胁通报的完整业务流程
3. **扩展性好**：通过代理机制实现了系统间的松耦合
4. **技术成熟**：使用了成熟稳定的技术栈

### 11.3 改进方向
1. **性能优化**：通过缓存、索引等手段提升系统性能
2. **可靠性提升**：增强系统的容错能力和监控能力
3. **架构演进**：向更现代的微服务和云原生架构演进
4. **技术升级**：持续跟进新技术，提升开发效率

通过本次深度分析，我们全面了解了威胁通报功能的实现机制和完整生命周期，为后续的系统优化和功能扩展提供了重要的技术参考。
