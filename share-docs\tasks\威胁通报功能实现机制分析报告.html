<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>aqsoc-main &#x5a01;&#x80c1;&#x901a;&#x62a5;&#x529f;&#x80fd;&#x5b9e;&#x73b0;&#x673a;&#x5236;&#x6df1;&#x5ea6;&#x5206;&#x6790;&#x62a5;&#x544a;</title>
            <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only],
.vscode-high-contrast:not(.vscode-high-contrast-light) img[src$=\#gh-light-mode-only],
.vscode-high-contrast-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

</style>
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex/dist/katex.min.css">
<link href="https://cdn.jsdelivr.net/npm/katex-copytex@latest/dist/katex-copytex.min.css" rel="stylesheet" type="text/css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item {
    list-style-type: none;
}

.task-list-item-checkbox {
    margin-left: -20px;
    vertical-align: middle;
    pointer-events: none;
}
</style>
<style>
:root {
  --color-note: #0969da;
  --color-tip: #1a7f37;
  --color-warning: #9a6700;
  --color-severe: #bc4c00;
  --color-caution: #d1242f;
  --color-important: #8250df;
}

</style>
<style>
@media (prefers-color-scheme: dark) {
  :root {
    --color-note: #2f81f7;
    --color-tip: #3fb950;
    --color-warning: #d29922;
    --color-severe: #db6d28;
    --color-caution: #f85149;
    --color-important: #a371f7;
  }
}

</style>
<style>
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 16px;
  color: inherit;
  border-left: .25em solid #888;
}

.markdown-alert>:first-child {
  margin-top: 0
}

.markdown-alert>:last-child {
  margin-bottom: 0
}

.markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1
}

.markdown-alert .markdown-alert-title .octicon {
  margin-right: 0.5rem;
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.markdown-alert.markdown-alert-note {
  border-left-color: var(--color-note);
}

.markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--color-note);
}

.markdown-alert.markdown-alert-important {
  border-left-color: var(--color-important);
}

.markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--color-important);
}

.markdown-alert.markdown-alert-warning {
  border-left-color: var(--color-warning);
}

.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--color-warning);
}

.markdown-alert.markdown-alert-tip {
  border-left-color: var(--color-tip);
}

.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--color-tip);
}

.markdown-alert.markdown-alert-caution {
  border-left-color: var(--color-caution);
}

.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--color-caution);
}

</style>
        
        </head>
        <body class="vscode-body vscode-light">
            <h1 id="aqsoc-main-威胁通报功能实现机制深度分析报告">aqsoc-main 威胁通报功能实现机制深度分析报告</h1>
<h2 id="1-功能概述">1. 功能概述</h2>
<h3 id="11-业务背景">1.1 业务背景</h3>
<p>威胁通报功能是aqsoc-main安全运营平台的核心业务模块，主要用于处理安全威胁事件的通报、流转和处置。该功能通过工作流引擎实现威胁事件的标准化处理流程，确保安全事件能够及时、有序地得到响应和处置。</p>
<h3 id="12-核心功能">1.2 核心功能</h3>
<ul>
<li><strong>威胁通报列表管理</strong>：展示待处理、处理中、已完成的威胁通报工单</li>
<li><strong>工作流程控制</strong>：基于JNPF工作流引擎的流程自动化处理</li>
<li><strong>统计分析</strong>：提供各类威胁事件的统计数据和处理状态分析</li>
<li><strong>多系统集成</strong>：通过代理机制实现与工作流引擎的无缝集成</li>
</ul>
<h3 id="13-技术特点">1.3 技术特点</h3>
<ul>
<li><strong>微服务架构</strong>：aqsoc-main主平台 + aq-jnpf工作流引擎分离部署</li>
<li><strong>代理集成</strong>：使用smiley-http-proxy-servlet实现跨服务调用</li>
<li><strong>数据分离</strong>：业务数据与工作流数据分库存储</li>
<li><strong>状态同步</strong>：双向数据同步确保业务状态一致性</li>
</ul>
<h2 id="2-整体架构">2. 整体架构</h2>
<h3 id="21-系统架构图">2.1 系统架构图</h3>
<pre><code class="language-mermaid">graph TB
    subgraph &quot;前端层&quot;
        A[Vue.js前端应用&lt;br/&gt;ruoyi-ui]
        A1[威胁通报页面&lt;br/&gt;workbench.vue]
        A2[工单管理页面&lt;br/&gt;work/order]
    end
    
    subgraph &quot;aqsoc-main 主平台&quot;
        B[aqsoc-admin&lt;br/&gt;Spring Boot应用]
        B1[ProxyServletConfiguration&lt;br/&gt;代理配置]
        B2[TblWorkOrderController&lt;br/&gt;工单控制器]
        B3[TblWorkOrderService&lt;br/&gt;工单服务]
        B4[TblWorkOrderMapper&lt;br/&gt;数据访问层]
    end
    
    subgraph &quot;代理层&quot;
        C[smiley-http-proxy-servlet&lt;br/&gt;HTTP代理]
        C1[URL映射: /proxy/*]
        C2[目标地址: ***************:30001]
    end
    
    subgraph &quot;aq-jnpf 工作流引擎&quot;
        D[jnpf-admin&lt;br/&gt;工作流应用]
        D1[FlowTemplateController&lt;br/&gt;流程模板控制器]
        D2[FlowTaskController&lt;br/&gt;流程任务控制器]
        D3[FlowEngineService&lt;br/&gt;流程引擎服务]
    end
    
    subgraph &quot;数据层&quot;
        E[(aqsoc数据库)]
        E1[tbl_work_order&lt;br/&gt;工单主表]
        E2[tbl_work_backlog&lt;br/&gt;待办表]
        E3[tbl_work_history&lt;br/&gt;历史表]
        
        F[(low_code_jnpf数据库)]
        F1[flow_engine&lt;br/&gt;流程引擎表]
        F2[flow_task&lt;br/&gt;流程任务表]
        F3[flow_template&lt;br/&gt;流程模板表]
    end
</code></pre>
<h3 id="22-架构层次说明">2.2 架构层次说明</h3>
<h4 id="221-前端层">2.2.1 前端层</h4>
<ul>
<li><strong>技术栈</strong>：Vue.js + Element UI</li>
<li><strong>主要页面</strong>：
<ul>
<li><code>workbench.vue</code>：首页工作台，展示威胁通报概览</li>
<li><code>work/order</code>：工单管理页面，提供详细的工单操作功能</li>
</ul>
</li>
<li><strong>API调用</strong>：通过axios发起HTTP请求到后端接口</li>
</ul>
<h4 id="222-应用层">2.2.2 应用层</h4>
<ul>
<li><strong>aqsoc-admin</strong>：主业务应用，处理威胁通报的核心业务逻辑</li>
<li><strong>代理配置</strong>：ProxyServletConfiguration配置HTTP代理</li>
<li><strong>控制器层</strong>：TblWorkOrderController处理前端请求</li>
<li><strong>服务层</strong>：TblWorkOrderService实现业务逻辑</li>
<li><strong>数据访问层</strong>：TblWorkOrderMapper执行数据库操作</li>
</ul>
<h4 id="223-代理层">2.2.3 代理层</h4>
<ul>
<li><strong>smiley-http-proxy-servlet</strong>：开源HTTP代理组件</li>
<li><strong>URL映射</strong>：将<code>/proxy/*</code>请求转发到工作流引擎</li>
<li><strong>透明代理</strong>：保持请求头、Cookie等信息不变</li>
</ul>
<h4 id="224-工作流引擎层">2.2.4 工作流引擎层</h4>
<ul>
<li><strong>aq-jnpf</strong>：独立的工作流引擎应用</li>
<li><strong>流程管理</strong>：FlowTemplateController管理流程模板</li>
<li><strong>任务处理</strong>：FlowTaskController处理流程任务</li>
<li><strong>引擎服务</strong>：FlowEngineService提供核心流程能力</li>
</ul>
<h4 id="225-数据层">2.2.5 数据层</h4>
<ul>
<li><strong>aqsoc数据库</strong>：存储业务数据（工单、待办、历史）</li>
<li><strong>low_code_jnpf数据库</strong>：存储工作流数据（流程、任务、模板）</li>
</ul>
<h2 id="3-代理机制详解">3. 代理机制详解</h2>
<h3 id="31-代理配置实现">3.1 代理配置实现</h3>
<h4 id="311-配置类分析">3.1.1 配置类分析</h4>
<pre><code class="language-java"><span class="hljs-meta">@Configuration</span>
<span class="hljs-keyword">public</span> <span class="hljs-keyword">class</span> <span class="hljs-title class_">ProxyServletConfiguration</span> {
    <span class="hljs-meta">@Value(&quot;${jnpf.proxy.servlet_url}&quot;)</span>
    <span class="hljs-keyword">private</span> String servlet_url; <span class="hljs-comment">// /proxy/*</span>
    
    <span class="hljs-meta">@Value(&quot;${jnpf.proxy.target_url}&quot;)</span>
    <span class="hljs-keyword">private</span> String target_url; <span class="hljs-comment">// http://***************:30001</span>
    
    <span class="hljs-meta">@Bean</span>
    <span class="hljs-keyword">public</span> ServletRegistrationBean <span class="hljs-title function_">proxyServletRegistration</span><span class="hljs-params">()</span> {
        <span class="hljs-type">ServletRegistrationBean</span> <span class="hljs-variable">registrationBean</span> <span class="hljs-operator">=</span> 
            <span class="hljs-keyword">new</span> <span class="hljs-title class_">ServletRegistrationBean</span>(createProxyServlet(), servlet_url);
        
        Map&lt;String, String&gt; params = MapUtil.builder(<span class="hljs-keyword">new</span> <span class="hljs-title class_">HashMap</span>&lt;String, String&gt;())
            .put(ProxyServlet.P_TARGET_URI, target_url)        <span class="hljs-comment">// 目标URI</span>
            .put(ProxyServlet.P_HANDLEREDIRECTS, <span class="hljs-string">&quot;false&quot;</span>)      <span class="hljs-comment">// 重定向处理</span>
            .put(ProxyServlet.P_PRESERVECOOKIES, <span class="hljs-string">&quot;true&quot;</span>)       <span class="hljs-comment">// 保持Cookie</span>
            .put(ProxyServlet.P_PRESERVEHOST, <span class="hljs-string">&quot;true&quot;</span>)          <span class="hljs-comment">// 保持Host</span>
            .put(ProxyServlet.P_LOG, <span class="hljs-string">&quot;true&quot;</span>)                   <span class="hljs-comment">// 启用日志</span>
            .build();
        
        registrationBean.setInitParameters(params);
        <span class="hljs-keyword">return</span> registrationBean;
    }
}
</code></pre>
<h4 id="312-配置参数说明">3.1.2 配置参数说明</h4>
<ul>
<li><strong>P_TARGET_URI</strong>：代理目标地址，指向aq-jnpf工作流引擎</li>
<li><strong>P_HANDLEREDIRECTS</strong>：设为false，由客户端处理重定向</li>
<li><strong>P_PRESERVECOOKIES</strong>：保持Cookie不变，确保会话状态</li>
<li><strong>P_PRESERVEHOST</strong>：保持Host头不变，避免跨域问题</li>
<li><strong>P_LOG</strong>：启用代理日志，便于调试和监控</li>
</ul>
<h3 id="32-url映射规则">3.2 URL映射规则</h3>
<h4 id="321-映射机制">3.2.1 映射机制</h4>
<pre><code>前端请求：/prod-api/proxy/api/workflow/Engine/flowTemplate/ListAll
↓
代理处理：移除 /proxy 前缀
↓
转发请求：http://***************:30001/api/workflow/Engine/flowTemplate/ListAll
</code></pre>
<h4 id="322-请求转发流程">3.2.2 请求转发流程</h4>
<ol>
<li><strong>请求拦截</strong>：Spring Boot拦截匹配<code>/proxy/*</code>的请求</li>
<li><strong>URL重写</strong>：移除<code>/proxy</code>前缀，保留后续路径</li>
<li><strong>请求转发</strong>：将重写后的URL转发到目标服务器</li>
<li><strong>响应返回</strong>：将目标服务器的响应原样返回给客户端</li>
</ol>
<h3 id="33-代理优势分析">3.3 代理优势分析</h3>
<h4 id="331-技术优势">3.3.1 技术优势</h4>
<ul>
<li><strong>透明集成</strong>：前端无需感知后端服务的具体部署位置</li>
<li><strong>统一入口</strong>：所有请求通过统一的API网关进行路由</li>
<li><strong>配置灵活</strong>：可通过配置文件动态调整目标服务地址</li>
<li><strong>会话保持</strong>：自动处理Cookie和会话状态</li>
</ul>
<h4 id="332-架构优势">3.3.2 架构优势</h4>
<ul>
<li><strong>服务解耦</strong>：主业务系统与工作流引擎独立部署</li>
<li><strong>扩展性强</strong>：可以轻松添加新的代理规则和目标服务</li>
<li><strong>维护简单</strong>：代理配置集中管理，便于运维</li>
<li><strong>性能优化</strong>：减少跨域请求的复杂性</li>
</ul>
<h2 id="4-关键代码分析">4. 关键代码分析</h2>
<h3 id="41-工单控制器核心方法">4.1 工单控制器核心方法</h3>
<h4 id="411-待办列表接口">4.1.1 待办列表接口</h4>
<pre><code class="language-java"><span class="hljs-meta">@GetMapping(&quot;/waitList&quot;)</span>
<span class="hljs-keyword">public</span> TableDataInfo <span class="hljs-title function_">list</span><span class="hljs-params">(TblWorkOrder tblWorkOrder)</span> {
    startPage();  <span class="hljs-comment">// 启动分页</span>
    List&lt;TblWorkOrder&gt; list = tblWorkOrderService.selectWaitList(tblWorkOrder);
    <span class="hljs-keyword">return</span> getDataTable(list);  <span class="hljs-comment">// 返回分页数据</span>
}
</code></pre>
<p><strong>功能说明</strong>：</p>
<ul>
<li>接收前端查询参数，包括分页信息和过滤条件</li>
<li>调用服务层方法查询待办工单列表</li>
<li>返回标准的分页数据格式</li>
</ul>
<h4 id="412-统计数据接口">4.1.2 统计数据接口</h4>
<pre><code class="language-java"><span class="hljs-meta">@GetMapping(&quot;/getStatistics&quot;)</span>
<span class="hljs-keyword">public</span> AjaxResult <span class="hljs-title function_">getStatistics</span><span class="hljs-params">(TblWorkOrder workOrder)</span>{
    <span class="hljs-keyword">return</span> AjaxResult.success(tblWorkOrderService.getStatisticsData(workOrder));
}
</code></pre>
<p><strong>功能说明</strong>：</p>
<ul>
<li>获取工单统计数据，包括各状态的工单数量</li>
<li>支持按部门、用户等维度进行统计</li>
<li>返回JSON格式的统计结果</li>
</ul>
<h3 id="42-服务层业务逻辑">4.2 服务层业务逻辑</h3>
<h4 id="421-待办列表查询">4.2.1 待办列表查询</h4>
<pre><code class="language-java"><span class="hljs-meta">@Override</span>
<span class="hljs-keyword">public</span> List&lt;TblWorkOrder&gt; <span class="hljs-title function_">selectWaitList</span><span class="hljs-params">(TblWorkOrder tblWorkOrder)</span> {
    handlePrem(tblWorkOrder);  <span class="hljs-comment">// 处理权限过滤</span>
    List&lt;TblWorkOrder&gt; tblWorkOrders = tblWorkOrderMapper.selectWaitList(tblWorkOrder);
    
    <span class="hljs-keyword">for</span> (TblWorkOrder workOrder : tblWorkOrders) {
        getFileUrls(workOrder);  <span class="hljs-comment">// 处理附件URL</span>
    }
    
    <span class="hljs-comment">// 处理历史节点属性</span>
    <span class="hljs-keyword">if</span>(CollUtil.isNotEmpty(tblWorkOrders)){
        tblWorkOrders.forEach(workOrder -&gt; {
            List&lt;String&gt; historyNodeProperties = workOrder.getHistoryNodeProperties();
            <span class="hljs-comment">// ... 节点属性处理逻辑</span>
        });
    }
    
    <span class="hljs-keyword">return</span> tblWorkOrders;
}
</code></pre>
<p><strong>业务逻辑</strong>：</p>
<ol>
<li><strong>权限处理</strong>：根据用户权限过滤可见的工单</li>
<li><strong>数据查询</strong>：执行复杂的关联查询获取工单数据</li>
<li><strong>附件处理</strong>：处理工单相关的附件URL</li>
<li><strong>节点属性</strong>：处理工作流节点的历史属性信息</li>
</ol>
<h4 id="422-统计数据计算">4.2.2 统计数据计算</h4>
<pre><code class="language-java"><span class="hljs-meta">@Override</span>
<span class="hljs-keyword">public</span> JSONObject <span class="hljs-title function_">getStatisticsData</span><span class="hljs-params">(TblWorkOrder workOrder)</span> {
    handlePrem(workOrder);  <span class="hljs-comment">// 权限处理</span>
    <span class="hljs-type">JSONObject</span> <span class="hljs-variable">statisticsData</span> <span class="hljs-operator">=</span> tblWorkOrderMapper.getStatisticsData(workOrder);
    
    <span class="hljs-type">int</span> <span class="hljs-variable">allCount</span> <span class="hljs-operator">=</span> <span class="hljs-number">0</span>;
    <span class="hljs-keyword">for</span> (String key : statisticsData.keySet()) {
        allCount += statisticsData.getIntValue(key,<span class="hljs-number">0</span>);
    }
    statisticsData.put(<span class="hljs-string">&quot;allCount&quot;</span>,allCount);  <span class="hljs-comment">// 计算总数</span>
    
    <span class="hljs-keyword">return</span> statisticsData;
}
</code></pre>
<p><strong>统计逻辑</strong>：</p>
<ol>
<li><strong>权限过滤</strong>：确保统计数据符合用户权限范围</li>
<li><strong>数据聚合</strong>：执行SQL聚合查询获取各状态统计</li>
<li><strong>总数计算</strong>：计算所有状态的工单总数</li>
<li><strong>结果封装</strong>：返回包含详细统计信息的JSON对象</li>
</ol>
<h2 id="5-接口调用流程">5. 接口调用流程</h2>
<h3 id="51-威胁通报列表查询流程">5.1 威胁通报列表查询流程</h3>
<pre><code class="language-mermaid">sequenceDiagram
    participant F as 前端Vue.js
    participant AC as aqsoc-admin Controller
    participant AS as aqsoc-admin Service
    participant PS as ProxyServlet
    participant JC as jnpf Controller
    participant JS as jnpf Service
    participant DB1 as aqsoc数据库
    participant DB2 as jnpf数据库
    
    Note over F,DB2: 威胁通报列表查询流程
    
    F-&gt;&gt;+AC: GET /work/order/waitList
    Note right of F: 查询参数：pageNum, pageSize, queryState等
    
    AC-&gt;&gt;+AS: selectWaitList(tblWorkOrder)
    AS-&gt;&gt;+DB1: 执行复杂SQL查询
    Note right of AS: 关联查询：tbl_work_order, tbl_work_backlog, tbl_work_history, sys_dept, sys_user
    
    DB1--&gt;&gt;-AS: 返回工单列表数据
    AS-&gt;&gt;AS: 处理附件URL、节点属性等业务逻辑
    AS--&gt;&gt;-AC: 返回处理后的工单列表
    AC--&gt;&gt;-F: 返回分页数据 TableDataInfo
</code></pre>
<h3 id="52-流程模板获取流程">5.2 流程模板获取流程</h3>
<pre><code class="language-mermaid">sequenceDiagram
    participant F as 前端Vue.js
    participant AC as aqsoc-admin Controller
    participant PS as ProxyServlet
    participant JC as jnpf Controller
    participant JS as jnpf Service
    participant DB2 as jnpf数据库
    
    Note over F,DB2: 流程模板获取流程
    
    F-&gt;&gt;+AC: GET /proxy/api/workflow/Engine/flowTemplate/ListAll
    AC-&gt;&gt;+PS: 代理转发请求
    Note right of PS: URL重写：/proxy/* → http://***************:30001/*
    
    PS-&gt;&gt;+JC: GET /api/workflow/Engine/flowTemplate/ListAll
    JC-&gt;&gt;+JS: flowTemplateService.getTreeList()
    JS-&gt;&gt;+DB2: 查询flow_engine表
    DB2--&gt;&gt;-JS: 返回流程模板数据
    JS--&gt;&gt;-JC: 返回FlowTemplateListVO列表
    JC--&gt;&gt;-PS: 返回ActionResult&lt;ListVO&gt;
    PS--&gt;&gt;-AC: 代理返回响应
    AC--&gt;&gt;-F: 返回流程模板列表
</code></pre>
<h3 id="53-关键接口说明">5.3 关键接口说明</h3>
<h4 id="531-主要接口列表">5.3.1 主要接口列表</h4>
<table>
<thead>
<tr>
<th>接口路径</th>
<th>方法</th>
<th>功能说明</th>
<th>数据源</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>/work/order/waitList</code></td>
<td>GET</td>
<td>获取待办工单列表</td>
<td>aqsoc数据库</td>
</tr>
<tr>
<td><code>/work/order/getStatistics</code></td>
<td>GET</td>
<td>获取工单统计数据</td>
<td>aqsoc数据库</td>
</tr>
<tr>
<td><code>/proxy/api/workflow/Engine/flowTemplate/ListAll</code></td>
<td>GET</td>
<td>获取流程模板列表</td>
<td>jnpf数据库</td>
</tr>
<tr>
<td><code>/proxy/api/workflow/Engine/FlowBefore/List</code></td>
<td>GET</td>
<td>获取待审批任务</td>
<td>jnpf数据库</td>
</tr>
</tbody>
</table>
<h4 id="532-接口参数说明">5.3.2 接口参数说明</h4>
<p><strong>waitList接口参数</strong>：</p>
<ul>
<li><code>pageNum</code>：页码</li>
<li><code>pageSize</code>：每页大小</li>
<li><code>queryState</code>：查询状态（-1:待发起, 1:待审核, 2:待处置, 3:待核验, 4:已完成）</li>
<li><code>handleDept</code>：处理部门ID</li>
<li><code>workName</code>：工单名称（模糊查询）</li>
<li><code>workType</code>：工单类型</li>
</ul>
<p><strong>getStatistics接口参数</strong>：</p>
<ul>
<li><code>handleDept</code>：处理部门ID</li>
<li><code>createBy</code>：创建人ID</li>
<li><code>queryAll</code>：是否查询全部数据</li>
</ul>
<h2 id="6-数据库设计">6. 数据库设计</h2>
<h3 id="61-aqsoc数据库表结构">6.1 aqsoc数据库表结构</h3>
<h4 id="611-tbl_work_order工单主表">6.1.1 tbl_work_order（工单主表）</h4>
<table>
<thead>
<tr>
<th>字段名</th>
<th>类型</th>
<th>说明</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>id</td>
<td>bigint</td>
<td>主键ID</td>
<td>自增</td>
</tr>
<tr>
<td>work_no</td>
<td>varchar(50)</td>
<td>工单编号</td>
<td>唯一标识</td>
</tr>
<tr>
<td>work_name</td>
<td>varchar(200)</td>
<td>工单名称</td>
<td>业务标题</td>
</tr>
<tr>
<td>work_type</td>
<td>varchar(10)</td>
<td>工单类型</td>
<td>0:漏洞 1:威胁</td>
</tr>
<tr>
<td>event_ids</td>
<td>text</td>
<td>关联事件ID</td>
<td>JSON数组</td>
</tr>
<tr>
<td>application_id</td>
<td>varchar(50)</td>
<td>对应系统ID</td>
<td>关联业务系统</td>
</tr>
<tr>
<td>handle_dept</td>
<td>bigint</td>
<td>处理部门</td>
<td>外键关联sys_dept</td>
</tr>
<tr>
<td>handle_user</td>
<td>bigint</td>
<td>处理用户</td>
<td>外键关联sys_user</td>
</tr>
<tr>
<td>expect_complete_time</td>
<td>datetime</td>
<td>计划完成时间</td>
<td>业务时限</td>
</tr>
<tr>
<td>flow_state</td>
<td>varchar(10)</td>
<td>流程状态</td>
<td>-1:待发起 0,2:待审核 1:待处置 3:待核验 4:已完成</td>
</tr>
<tr>
<td>prod_id</td>
<td>varchar(50)</td>
<td>工作流实例ID</td>
<td>关联jnpf流程</td>
</tr>
<tr>
<td>complete_time</td>
<td>datetime</td>
<td>实际完成时间</td>
<td>完成时间戳</td>
</tr>
<tr>
<td>remark2</td>
<td>text</td>
<td>节点属性</td>
<td>工作流节点配置</td>
</tr>
<tr>
<td>remark5</td>
<td>varchar(50)</td>
<td>创建人ID</td>
<td>业务创建人</td>
</tr>
</tbody>
</table>
<h4 id="612-tbl_work_backlog待办表">6.1.2 tbl_work_backlog（待办表）</h4>
<table>
<thead>
<tr>
<th>字段名</th>
<th>类型</th>
<th>说明</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>id</td>
<td>bigint</td>
<td>主键ID</td>
<td>自增</td>
</tr>
<tr>
<td>work_id</td>
<td>bigint</td>
<td>工单ID</td>
<td>外键关联tbl_work_order</td>
</tr>
<tr>
<td>handle_user</td>
<td>bigint</td>
<td>处理人</td>
<td>待办任务分配人</td>
</tr>
<tr>
<td>is_completion</td>
<td>int</td>
<td>是否完成</td>
<td>0:未完成 1:已完成</td>
</tr>
<tr>
<td>completion_time</td>
<td>datetime</td>
<td>完成时间</td>
<td>任务完成时间</td>
</tr>
<tr>
<td>node_id</td>
<td>varchar(50)</td>
<td>所属节点</td>
<td>工作流节点ID</td>
</tr>
<tr>
<td>flow_state</td>
<td>varchar(10)</td>
<td>流程状态</td>
<td>当前流程状态</td>
</tr>
</tbody>
</table>
<h4 id="613-tbl_work_history历史表">6.1.3 tbl_work_history（历史表）</h4>
<table>
<thead>
<tr>
<th>字段名</th>
<th>类型</th>
<th>说明</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>id</td>
<td>bigint</td>
<td>主键ID</td>
<td>自增</td>
</tr>
<tr>
<td>work_id</td>
<td>bigint</td>
<td>工单ID</td>
<td>外键关联tbl_work_order</td>
</tr>
<tr>
<td>handle_state</td>
<td>varchar(10)</td>
<td>处理状态</td>
<td>历史状态记录</td>
</tr>
<tr>
<td>handle_user</td>
<td>varchar(50)</td>
<td>处理用户</td>
<td>操作人员</td>
</tr>
<tr>
<td>node_properties</td>
<td>text</td>
<td>节点配置</td>
<td>节点属性JSON</td>
</tr>
<tr>
<td>node_code</td>
<td>varchar(50)</td>
<td>当前节点</td>
<td>节点编码</td>
</tr>
<tr>
<td>type</td>
<td>int</td>
<td>类型</td>
<td>1:普通 2:抄送</td>
</tr>
<tr>
<td>create_time</td>
<td>datetime</td>
<td>创建时间</td>
<td>操作时间</td>
</tr>
</tbody>
</table>
<h3 id="62-low_code_jnpf数据库表结构">6.2 low_code_jnpf数据库表结构</h3>
<h4 id="621-flow_engine流程引擎表">6.2.1 flow_engine（流程引擎表）</h4>
<table>
<thead>
<tr>
<th>字段名</th>
<th>类型</th>
<th>说明</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>F_Id</td>
<td>varchar(50)</td>
<td>主键ID</td>
<td>UUID</td>
</tr>
<tr>
<td>F_EnCode</td>
<td>varchar(50)</td>
<td>流程编码</td>
<td>唯一编码</td>
</tr>
<tr>
<td>F_FullName</td>
<td>varchar(200)</td>
<td>流程名称</td>
<td>显示名称</td>
</tr>
<tr>
<td>F_Type</td>
<td>int</td>
<td>流程类型</td>
<td>流程分类</td>
</tr>
<tr>
<td>F_Category</td>
<td>varchar(50)</td>
<td>流程分类</td>
<td>业务分类</td>
</tr>
<tr>
<td>F_Form</td>
<td>varchar(50)</td>
<td>流程表单</td>
<td>关联表单ID</td>
</tr>
<tr>
<td>F_FlowTemplateJson</td>
<td>longtext</td>
<td>流程模板</td>
<td>流程定义JSON</td>
</tr>
<tr>
<td>F_FormTemplateJson</td>
<td>longtext</td>
<td>表单模板</td>
<td>表单定义JSON</td>
</tr>
<tr>
<td>F_EnabledMark</td>
<td>int</td>
<td>有效标志</td>
<td>0:禁用 1:启用</td>
</tr>
</tbody>
</table>
<h4 id="622-flow_task流程任务表">6.2.2 flow_task（流程任务表）</h4>
<table>
<thead>
<tr>
<th>字段名</th>
<th>类型</th>
<th>说明</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>F_Id</td>
<td>varchar(50)</td>
<td>主键ID</td>
<td>UUID</td>
</tr>
<tr>
<td>F_ProcessId</td>
<td>varchar(50)</td>
<td>流程实例ID</td>
<td>流程实例标识</td>
</tr>
<tr>
<td>F_EnCode</td>
<td>varchar(50)</td>
<td>任务编码</td>
<td>任务唯一编码</td>
</tr>
<tr>
<td>F_FullName</td>
<td>varchar(200)</td>
<td>任务名称</td>
<td>任务显示名称</td>
</tr>
<tr>
<td>F_FlowId</td>
<td>varchar(50)</td>
<td>流程ID</td>
<td>关联flow_engine</td>
</tr>
<tr>
<td>F_Status</td>
<td>int</td>
<td>任务状态</td>
<td>0:待处理 1:已处理 2:已撤回</td>
</tr>
<tr>
<td>F_StartTime</td>
<td>datetime</td>
<td>开始时间</td>
<td>任务开始时间</td>
</tr>
<tr>
<td>F_EndTime</td>
<td>datetime</td>
<td>结束时间</td>
<td>任务完成时间</td>
</tr>
</tbody>
</table>
<h3 id="63-数据关联关系">6.3 数据关联关系</h3>
<h4 id="631-跨库关联">6.3.1 跨库关联</h4>
<pre><code>aqsoc.tbl_work_order.prod_id ←→ low_code_jnpf.flow_task.F_ProcessId
</code></pre>
<p>通过流程实例ID建立业务工单与工作流任务的关联关系。</p>
<h4 id="632-状态映射">6.3.2 状态映射</h4>
<table>
<thead>
<tr>
<th>aqsoc.flow_state</th>
<th>说明</th>
<th>jnpf.F_Status</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>-1</td>
<td>待发起</td>
<td>-</td>
<td>未创建流程</td>
</tr>
<tr>
<td>0,2</td>
<td>待审核</td>
<td>0</td>
<td>待处理</td>
</tr>
<tr>
<td>1</td>
<td>待处置</td>
<td>0</td>
<td>待处理</td>
</tr>
<tr>
<td>3</td>
<td>待核验</td>
<td>0</td>
<td>待处理</td>
</tr>
<tr>
<td>4</td>
<td>已完成</td>
<td>1</td>
<td>已处理</td>
</tr>
</tbody>
</table>
<h2 id="7-配置说明">7. 配置说明</h2>
<h3 id="71-代理配置">7.1 代理配置</h3>
<pre><code class="language-yaml"><span class="hljs-comment"># application.yml</span>
<span class="hljs-attr">jnpf:</span>
  <span class="hljs-attr">proxy:</span>
    <span class="hljs-attr">servlet_url:</span> <span class="hljs-string">/proxy/*</span>                    <span class="hljs-comment"># 代理URL模式</span>
    <span class="hljs-attr">target_url:</span> <span class="hljs-string">http://***************:30001</span> <span class="hljs-comment"># 目标服务地址</span>
</code></pre>
<h3 id="72-数据库配置">7.2 数据库配置</h3>
<pre><code class="language-yaml"><span class="hljs-comment"># aqsoc数据库配置</span>
<span class="hljs-attr">spring:</span>
  <span class="hljs-attr">datasource:</span>
    <span class="hljs-attr">primary:</span>
      <span class="hljs-attr">url:</span> <span class="hljs-string">*********************************</span>
      <span class="hljs-attr">username:</span> <span class="hljs-string">root</span>
      <span class="hljs-attr">password:</span> <span class="hljs-string">password</span>
      
<span class="hljs-comment"># jnpf数据库配置（通过代理访问）</span>
<span class="hljs-attr">jnpf:</span>
  <span class="hljs-attr">datasource:</span>
    <span class="hljs-attr">url:</span> <span class="hljs-string">***********************************************</span>
    <span class="hljs-attr">username:</span> <span class="hljs-string">jnpf</span>
    <span class="hljs-attr">password:</span> <span class="hljs-string">jnpf123</span>
</code></pre>
<h3 id="73-maven依赖">7.3 Maven依赖</h3>
<pre><code class="language-xml"><span class="hljs-comment">&lt;!-- 代理组件 --&gt;</span>
<span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.mitre.dsmiley.httpproxy<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>smiley-http-proxy-servlet<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">version</span>&gt;</span>1.12.1<span class="hljs-tag">&lt;/<span class="hljs-name">version</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span>
</code></pre>
<h2 id="8-问题分析">8. 问题分析</h2>
<h3 id="81-现有问题">8.1 现有问题</h3>
<h4 id="811-性能问题">8.1.1 性能问题</h4>
<ol>
<li><strong>代理延迟</strong>：每次工作流接口调用都需要经过HTTP代理，增加了网络延迟</li>
<li><strong>数据库查询复杂</strong>：waitList接口的SQL查询涉及多表关联，性能较差</li>
<li><strong>重复查询</strong>：统计接口和列表接口可能存在重复的数据查询</li>
</ol>
<h4 id="812-可靠性问题">8.1.2 可靠性问题</h4>
<ol>
<li><strong>单点故障</strong>：工作流引擎服务不可用时，整个威胁通报功能受影响</li>
<li><strong>数据一致性</strong>：跨库数据同步可能存在延迟或不一致</li>
<li><strong>事务处理</strong>：跨系统操作缺乏分布式事务保障</li>
</ol>
<h4 id="813-维护问题">8.1.3 维护问题</h4>
<ol>
<li><strong>配置复杂</strong>：需要维护两套独立的应用和数据库</li>
<li><strong>调试困难</strong>：跨系统调用的问题定位和调试比较复杂</li>
<li><strong>版本兼容</strong>：两个系统的版本升级需要考虑兼容性</li>
</ol>
<h3 id="82-潜在风险">8.2 潜在风险</h3>
<h4 id="821-安全风险">8.2.1 安全风险</h4>
<ol>
<li><strong>代理安全</strong>：HTTP代理可能成为安全攻击的入口点</li>
<li><strong>数据泄露</strong>：跨网络传输的数据存在泄露风险</li>
<li><strong>权限控制</strong>：跨系统的权限控制可能存在漏洞</li>
</ol>
<h4 id="822-扩展性风险">8.2.2 扩展性风险</h4>
<ol>
<li><strong>性能瓶颈</strong>：随着数据量增长，现有架构可能成为性能瓶颈</li>
<li><strong>功能扩展</strong>：新增功能需要同时修改两个系统</li>
<li><strong>技术债务</strong>：代理机制增加了系统的复杂性</li>
</ol>
<h2 id="9-优化建议">9. 优化建议</h2>
<h3 id="91-短期优化">9.1 短期优化</h3>
<h4 id="911-性能优化">9.1.1 性能优化</h4>
<ol>
<li>
<p><strong>SQL优化</strong>：</p>
<ul>
<li>为waitList查询添加合适的数据库索引</li>
<li>优化复杂关联查询，考虑分步查询</li>
<li>实现查询结果缓存机制</li>
</ul>
</li>
<li>
<p><strong>接口优化</strong>：</p>
<ul>
<li>实现接口响应缓存</li>
<li>添加接口限流和熔断机制</li>
<li>优化数据传输格式，减少网络开销</li>
</ul>
</li>
<li>
<p><strong>代理优化</strong>：</p>
<ul>
<li>配置连接池，提高代理性能</li>
<li>启用HTTP/2协议，减少连接开销</li>
<li>实现代理请求的负载均衡</li>
</ul>
</li>
</ol>
<h4 id="912-可靠性优化">9.1.2 可靠性优化</h4>
<ol>
<li>
<p><strong>监控告警</strong>：</p>
<ul>
<li>添加接口调用监控和告警</li>
<li>实现数据库连接监控</li>
<li>配置系统健康检查</li>
</ul>
</li>
<li>
<p><strong>容错处理</strong>：</p>
<ul>
<li>实现接口调用重试机制</li>
<li>添加降级处理逻辑</li>
<li>配置熔断器防止雪崩</li>
</ul>
</li>
<li>
<p><strong>数据同步</strong>：</p>
<ul>
<li>实现数据同步状态监控</li>
<li>添加数据一致性校验</li>
<li>配置数据修复机制</li>
</ul>
</li>
</ol>
<h3 id="92-中期优化">9.2 中期优化</h3>
<h4 id="921-架构优化">9.2.1 架构优化</h4>
<ol>
<li>
<p><strong>服务网格</strong>：</p>
<ul>
<li>引入Istio等服务网格技术</li>
<li>实现更好的服务治理和监控</li>
<li>提供统一的安全策略</li>
</ul>
</li>
<li>
<p><strong>消息队列</strong>：</p>
<ul>
<li>使用MQ实现异步数据同步</li>
<li>减少系统间的直接依赖</li>
<li>提高系统的可扩展性</li>
</ul>
</li>
<li>
<p><strong>API网关</strong>：</p>
<ul>
<li>替换简单的HTTP代理</li>
<li>提供统一的API管理</li>
<li>实现更好的安全控制</li>
</ul>
</li>
</ol>
<h4 id="922-数据优化">9.2.2 数据优化</h4>
<ol>
<li>
<p><strong>数据库优化</strong>：</p>
<ul>
<li>考虑读写分离架构</li>
<li>实现数据分片策略</li>
<li>优化数据库连接池配置</li>
</ul>
</li>
<li>
<p><strong>缓存策略</strong>：</p>
<ul>
<li>引入Redis等缓存中间件</li>
<li>实现多级缓存架构</li>
<li>配置缓存失效策略</li>
</ul>
</li>
</ol>
<h3 id="93-长期优化">9.3 长期优化</h3>
<h4 id="931-微服务化">9.3.1 微服务化</h4>
<ol>
<li>
<p><strong>服务拆分</strong>：</p>
<ul>
<li>将威胁通报功能独立为微服务</li>
<li>实现更细粒度的服务划分</li>
<li>提高系统的可维护性</li>
</ul>
</li>
<li>
<p><strong>容器化部署</strong>：</p>
<ul>
<li>使用Docker容器化部署</li>
<li>引入Kubernetes进行容器编排</li>
<li>实现自动化运维</li>
</ul>
</li>
<li>
<p><strong>云原生架构</strong>：</p>
<ul>
<li>迁移到云原生架构</li>
<li>利用云服务提供的能力</li>
<li>实现更好的弹性伸缩</li>
</ul>
</li>
</ol>
<h4 id="932-技术升级">9.3.2 技术升级</h4>
<ol>
<li>
<p><strong>框架升级</strong>：</p>
<ul>
<li>升级Spring Boot到最新版本</li>
<li>引入Spring Cloud微服务框架</li>
<li>使用更现代的技术栈</li>
</ul>
</li>
<li>
<p><strong>工作流引擎</strong>：</p>
<ul>
<li>考虑使用更轻量级的工作流引擎</li>
<li>实现工作流引擎的云原生化</li>
<li>提供更好的可视化能力</li>
</ul>
</li>
</ol>
<h2 id="10-威胁通报工作流完整生命周期详解">10. 威胁通报工作流完整生命周期详解</h2>
<h3 id="101-生命周期概览">10.1 生命周期概览</h3>
<p>威胁通报工作流从创建到完成经历以下关键阶段：</p>
<pre><code class="language-mermaid">stateDiagram-v2
    [*] --&gt; 草稿状态
    草稿状态 --&gt; 待发起: 保存草稿
    待发起 --&gt; 待审核: 提交流程
    待审核 --&gt; 待处置: 审核通过
    待审核 --&gt; 待发起: 驳回到发起人
    待处置 --&gt; 待核验: 处置完成
    待核验 --&gt; 已完成: 核验通过
    待核验 --&gt; 待处置: 核验不通过

    待审核 --&gt; 转办中: 转办操作
    转办中 --&gt; 待审核: 转办完成

    待发起 --&gt; 撤回: 撤回操作
    待审核 --&gt; 撤回: 撤回操作
    待处置 --&gt; 撤回: 撤回操作

    已完成 --&gt; [*]
    撤回 --&gt; [*]
</code></pre>
<h3 id="102-详细生命周期流程">10.2 详细生命周期流程</h3>
<h4 id="1021-阶段一工单创建与草稿保存">10.2.1 阶段一：工单创建与草稿保存</h4>
<p><strong>触发条件</strong>：用户在前端创建新的威胁通报工单</p>
<p><strong>核心代码流程</strong>：</p>
<p>&lt;augment_code_snippet path=&quot;aqsoc-monitor/src/main/java/com/ruoyi/work/controller/TblWorkOrderController.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-comment">/**
 * 新增工单主表
 */</span>
<span class="hljs-meta">@Log(title = &quot;工单主表&quot;, businessType = BusinessType.INSERT)</span>
<span class="hljs-meta">@PostMapping(&quot;/{processId}&quot;)</span>
<span class="hljs-keyword">public</span> AjaxResult <span class="hljs-title function_">add</span><span class="hljs-params">(<span class="hljs-meta">@PathVariable(&quot;processId&quot;)</span> String processId, <span class="hljs-meta">@RequestBody</span> TblWorkOrder tblWorkOrder)</span> {
    <span class="hljs-comment">// 加入流程实例id</span>
    logger.info(<span class="hljs-string">&quot;新增流程：{}，Form：{}&quot;</span>,processId,tblWorkOrder);
    <span class="hljs-comment">//todo 暂存 可能没有用户 那就是发起人处理</span>
    tblWorkOrder.setProdId(processId);
    <span class="hljs-keyword">return</span> toAjax(tblWorkOrderService.insertTblWorkOrder(tblWorkOrder));
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<p>&lt;augment_code_snippet path=&quot;aqsoc-monitor/src/main/java/com/ruoyi/work/service/impl/TblWorkOrderServiceImpl.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-comment">/**
 * 新增工单主表
 */</span>
<span class="hljs-meta">@Transactional(rollbackFor = Exception.class)</span>
<span class="hljs-meta">@Override</span>
<span class="hljs-keyword">public</span> <span class="hljs-type">int</span> <span class="hljs-title function_">insertTblWorkOrder</span><span class="hljs-params">(TblWorkOrder tblWorkOrder)</span> {
    <span class="hljs-type">Long</span> <span class="hljs-variable">workID</span> <span class="hljs-operator">=</span> snowflake.nextId();
    <span class="hljs-keyword">if</span>(StrUtil.isBlank(tblWorkOrder.getWorkName())){
        <span class="hljs-keyword">return</span> <span class="hljs-number">1</span>;
    }

    <span class="hljs-comment">// 设置工单基本信息</span>
    tblWorkOrder.setId(workID);
    tblWorkOrder.setCreateTime(DateUtils.getNowDate());
    tblWorkOrder.setCreateBy(getUsername());

    <span class="hljs-comment">// 生成工单编号</span>
    <span class="hljs-type">String</span> <span class="hljs-variable">workNo</span> <span class="hljs-operator">=</span> generateWorkNo();
    tblWorkOrder.setWorkNo(workNo);

    <span class="hljs-comment">// 设置默认状态为待发起</span>
    <span class="hljs-keyword">if</span>(tblWorkOrder.getFlowState() == <span class="hljs-literal">null</span>){
        tblWorkOrder.setFlowState(<span class="hljs-string">&quot;-1&quot;</span>); <span class="hljs-comment">// 待发起状态</span>
    }

    <span class="hljs-comment">//保存通报信息</span>
    <span class="hljs-type">int</span> <span class="hljs-variable">i</span> <span class="hljs-operator">=</span> tblWorkOrderMapper.insertTblWorkOrder(tblWorkOrder);
    <span class="hljs-comment">//保存通报对象信息</span>
    List&lt;JSONObject&gt; reportTargetForm = tblWorkOrder.getReportTargetForm();
    handleReportTargetForm(reportTargetForm,tblWorkOrder);

    <span class="hljs-keyword">return</span> i;
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<p>&lt;augment_code_snippet path=&quot;jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowDynamicServiceImpl.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-meta">@Override</span>
<span class="hljs-meta">@DSTransactional</span>
<span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">flowTask</span><span class="hljs-params">(FlowModel flowModel, FlowStatusEnum flowStatus, String formId)</span> <span class="hljs-keyword">throws</span> WorkFlowException {
    <span class="hljs-comment">//流程数据</span>
    <span class="hljs-keyword">switch</span> (flowStatus) {
        <span class="hljs-keyword">case</span> save:
            <span class="hljs-type">FlowTaskEntity</span> <span class="hljs-variable">taskEntity</span> <span class="hljs-operator">=</span> flowTaskNewService.save(flowModel);
            <span class="hljs-type">JSONObject</span> <span class="hljs-variable">resData</span> <span class="hljs-operator">=</span> flowModel.getResData();
            resData.put(<span class="hljs-string">&quot;flowTask&quot;</span>,taskEntity);
            flowModel.setResData(resData);
            <span class="hljs-keyword">break</span>;
        <span class="hljs-keyword">case</span> submit:
            <span class="hljs-comment">//todo 获取表单最新数据</span>
            flowModel.setFormData(flowTaskUtil.infoData(formId, flowModel.getProcessId()));
            flowTaskNewService.submitAll(flowModel);
            <span class="hljs-keyword">break</span>;
        <span class="hljs-keyword">default</span>:
            <span class="hljs-keyword">break</span>;
    }
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<p><strong>状态变化</strong>：</p>
<ul>
<li><strong>工单状态</strong>：<code>flow_state = &quot;-1&quot;</code> (待发起)</li>
<li><strong>工作流状态</strong>：<code>F_Status = 0</code> (草稿状态)</li>
<li><strong>数据库操作</strong>：
<ul>
<li>插入 <code>tbl_work_order</code> 记录</li>
<li>创建 <code>flow_task</code> 实例</li>
<li>生成流程实例ID (<code>prod_id</code>)</li>
</ul>
</li>
</ul>
<h4 id="1022-阶段二流程正式提交">10.2.2 阶段二：流程正式提交</h4>
<p><strong>触发条件</strong>：用户点击&quot;提交&quot;按钮，正式发起工作流</p>
<p><strong>核心代码流程</strong>：</p>
<p>&lt;augment_code_snippet path=&quot;jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-meta">@Override</span>
<span class="hljs-meta">@DSTransactional</span>
<span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">submit</span><span class="hljs-params">(FlowModel flowModel)</span> <span class="hljs-keyword">throws</span> WorkFlowException {
    <span class="hljs-type">UserInfo</span> <span class="hljs-variable">userInfo</span> <span class="hljs-operator">=</span> flowModel.getUserInfo();
    flowModel.setStatus(FlowStatusEnum.submit.getMessage());
    <span class="hljs-comment">//流程节点</span>
    List&lt;FlowTaskNodeEntity&gt; taskNodeList = <span class="hljs-keyword">new</span> <span class="hljs-title class_">ArrayList</span>&lt;&gt;();
    List&lt;ChildNodeList&gt; nodeListAll = <span class="hljs-keyword">new</span> <span class="hljs-title class_">ArrayList</span>&lt;&gt;();
    <span class="hljs-comment">//流程经办</span>
    List&lt;FlowTaskOperatorEntity&gt; operatorList = <span class="hljs-keyword">new</span> <span class="hljs-title class_">ArrayList</span>&lt;&gt;();
    <span class="hljs-type">FlowTaskEntity</span> <span class="hljs-variable">flowTask</span> <span class="hljs-operator">=</span> save(flowModel);
    <span class="hljs-type">boolean</span> <span class="hljs-variable">isRejectId</span> <span class="hljs-operator">=</span> StringUtil.isEmpty(flowTask.getRejectId());

    <span class="hljs-keyword">if</span> (isRejectId) {
        <span class="hljs-comment">//发起用户信息</span>
        flowTaskUtil.flowUser(flowTask);
        flowTask.setStartTime(<span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>());
        flowModel.setTaskOperatorId(FlowNature.ParentId);

        <span class="hljs-comment">// 处理开始节点</span>
        <span class="hljs-type">ChildNodeList</span> <span class="hljs-variable">start</span> <span class="hljs-operator">=</span> flowTaskUtil.startNode(flowModel.getFlowId());

        <span class="hljs-comment">// 创建开始节点记录</span>
        <span class="hljs-type">FlowTaskNodeEntity</span> <span class="hljs-variable">startNode</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">FlowTaskNodeEntity</span>();
        startNode.setTaskId(flowTask.getId());
        startNode.setNodeCode(start.getCustom().getNodeId());
        startNode.setNodeName(start.getProperties().getTitle());
        startNode.setNodeType(start.getType());
        startNode.setCompletion(FlowNature.AuditCompletion);
        startNode.setState(FlowNodeEnum.Process.getCode());
        taskNodeList.add(startNode);

        <span class="hljs-comment">// 流程流转到下一节点</span>
        flowTaskUtil.nextNode(flowTask, start, operatorList, taskNodeList, flowModel);
    }

    <span class="hljs-comment">// 更新流程状态</span>
    flowTask.setStatus(FlowTaskStatusEnum.Handle.getCode());
    flowTask.setRejectId(<span class="hljs-literal">null</span>);
    flowTaskService.update(flowTask);

    <span class="hljs-keyword">if</span> (isRejectId) {
        <span class="hljs-comment">//获取抄送人</span>
        List&lt;FlowTaskCirculateEntity&gt; circulateList = <span class="hljs-keyword">new</span> <span class="hljs-title class_">ArrayList</span>&lt;&gt;();
        flowTaskUtil.circulateList(start, circulateList, flowModel, flowTask);
        flowTaskCirculateService.create(circulateList);

        <span class="hljs-comment">//自动审批</span>
        <span class="hljs-type">FlowApproveModel</span> <span class="hljs-variable">approveModel</span> <span class="hljs-operator">=</span> FlowApproveModel.builder()
            .operatorList(operatorList)
            .taskNodeList(taskNodeList)
            .flowTask(flowTask)
            .flowModel(flowModel)
            .isSubmit(<span class="hljs-literal">true</span>)
            .build();
        flowTaskUtil.approve(approveModel);
    }
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<p><strong>状态变化</strong>：</p>
<ul>
<li><strong>工单状态</strong>：<code>flow_state = &quot;2&quot;</code> (待审核)</li>
<li><strong>工作流状态</strong>：<code>F_Status = 0</code> (待处理)</li>
<li><strong>数据库操作</strong>：
<ul>
<li>更新 <code>tbl_work_order.flow_state</code></li>
<li>创建 <code>flow_task_node</code> 节点记录</li>
<li>创建 <code>flow_task_operator</code> 经办人记录</li>
<li>创建 <code>flow_task_circulate</code> 抄送记录</li>
<li>插入 <code>tbl_work_backlog</code> 待办任务</li>
</ul>
</li>
</ul>
<p><strong>抄送机制</strong>：</p>
<p>&lt;augment_code_snippet path=&quot;jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/util/FlowTaskUtil.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-comment">// 抄送人处理逻辑</span>
<span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">circulateList</span><span class="hljs-params">(ChildNodeList nodeModel, List&lt;FlowTaskCirculateEntity&gt; circulateList,
                         FlowModel flowModel, FlowTaskEntity flowTask)</span> {
    <span class="hljs-type">Properties</span> <span class="hljs-variable">properties</span> <span class="hljs-operator">=</span> nodeModel.getProperties();
    List&lt;String&gt; userIdAll = <span class="hljs-keyword">new</span> <span class="hljs-title class_">ArrayList</span>&lt;&gt;();
    userIdAll.addAll(properties.getCirculateUser());  <span class="hljs-comment">// 直接指定用户</span>
    userIdAll.addAll(properties.getCirculateRole());  <span class="hljs-comment">// 角色用户</span>
    userIdAll.addAll(properties.getCirculatePosition()); <span class="hljs-comment">// 职位用户</span>
    userIdAll.addAll(properties.getCirculateGroup());    <span class="hljs-comment">// 用户组</span>
    userIdAll.addAll(properties.getCirculateOrg());      <span class="hljs-comment">// 组织用户</span>

    <span class="hljs-comment">// 生成抄送实体</span>
    <span class="hljs-keyword">for</span> (String userId : userIdAll) {
        <span class="hljs-type">FlowTaskCirculateEntity</span> <span class="hljs-variable">entity</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">FlowTaskCirculateEntity</span>();
        entity.setTaskId(flowTask.getId());
        entity.setCirculateUserId(userId);
        entity.setCreatorTime(<span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>());
        circulateList.add(entity);
    }
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<h4 id="1023-阶段三审核节点处理">10.2.3 阶段三：审核节点处理</h4>
<p><strong>触发条件</strong>：审核人员对待审核的工单进行审批操作</p>
<p><strong>审核通过流程</strong>：</p>
<p>&lt;augment_code_snippet path=&quot;jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-meta">@Override</span>
<span class="hljs-meta">@DSTransactional</span>
<span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">audit</span><span class="hljs-params">(FlowTaskEntity flowTask, FlowTaskOperatorEntity operator, FlowModel flowModel)</span> <span class="hljs-keyword">throws</span> WorkFlowException {
    <span class="hljs-type">UserInfo</span> <span class="hljs-variable">userInfo</span> <span class="hljs-operator">=</span> flowModel.getUserInfo();
    <span class="hljs-comment">//判断是否审批过</span>
    <span class="hljs-keyword">if</span> (!FlowNature.ProcessCompletion.equals(operator.getCompletion())) {
        <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">WorkFlowException</span>(MsgCode.WF005.get());
    }
    <span class="hljs-comment">//判断流程是否处于挂起状态</span>
    flowTaskUtil.isSuspend(flowTask);

    <span class="hljs-comment">// 更新经办人状态</span>
    operator.setCompletion(FlowNature.AuditCompletion);
    operator.setHandleTime(<span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>());
    operator.setHandleOpinion(flowModel.getHandleOpinion());
    flowTaskOperatorService.update(operator);

    <span class="hljs-comment">// 创建审批记录</span>
    <span class="hljs-type">FlowTaskOperatorRecordEntity</span> <span class="hljs-variable">operatorRecord</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">FlowTaskOperatorRecordEntity</span>();
    operatorRecord.setTaskId(flowTask.getId());
    operatorRecord.setHandleId(operator.getHandleId());
    operatorRecord.setHandleStatus(FlowRecordEnum.audit.getCode());
    operatorRecord.setHandleOpinion(flowModel.getHandleOpinion());
    operatorRecord.setHandleTime(<span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>());
    flowTaskOperatorRecordService.create(operatorRecord);

    <span class="hljs-comment">// 获取当前节点信息</span>
    List&lt;FlowTaskNodeEntity&gt; taskNodeList = flowTaskNodeService.getList(flowTask.getId());
    <span class="hljs-type">FlowTaskNodeEntity</span> <span class="hljs-variable">flowTaskNode</span> <span class="hljs-operator">=</span> taskNodeList.stream()
        .filter(t -&gt; operator.getNodeCode().equals(t.getNodeCode()))
        .findFirst().orElse(<span class="hljs-literal">null</span>);

    <span class="hljs-comment">// 流程流转到下一节点</span>
    flowTaskUtil.auditTaskNode(templateAllModel, flowTask, flowTaskNode, flowModel);
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<p><strong>状态变化</strong>：</p>
<ul>
<li><strong>工单状态</strong>：<code>flow_state = &quot;1&quot;</code> (待处置)</li>
<li><strong>工作流状态</strong>：当前节点完成，流转到下一节点</li>
<li><strong>数据库操作</strong>：
<ul>
<li>更新 <code>flow_task_operator.completion</code></li>
<li>插入 <code>flow_task_operator_record</code> 审批记录</li>
<li>更新 <code>tbl_work_order.flow_state</code></li>
<li>更新 <code>tbl_work_backlog</code> 待办状态</li>
<li>插入 <code>tbl_work_history</code> 历史记录</li>
</ul>
</li>
</ul>
<p><strong>驳回流程</strong>：</p>
<p>&lt;augment_code_snippet path=&quot;jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-meta">@Override</span>
<span class="hljs-meta">@DSTransactional</span>
<span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">reject</span><span class="hljs-params">(FlowTaskEntity flowTask, FlowTaskOperatorEntity operator, FlowModel flowModel)</span> <span class="hljs-keyword">throws</span> WorkFlowException {
    <span class="hljs-type">UserInfo</span> <span class="hljs-variable">userInfo</span> <span class="hljs-operator">=</span> flowModel.getUserInfo();
    <span class="hljs-type">String</span> <span class="hljs-variable">userId</span> <span class="hljs-operator">=</span> StringUtil.isNotEmpty(flowModel.getUserId()) ? flowModel.getUserId() : userInfo.getUserId();
    List&lt;String&gt; rejectThisStepId = Arrays.asList(flowTask.getThisStepId().split(<span class="hljs-string">&quot;,&quot;</span> ));

    <span class="hljs-comment">//判断流程是否处于挂起状态</span>
    flowTaskUtil.isSuspend(flowTask);

    <span class="hljs-comment">// 确定驳回目标节点</span>
    <span class="hljs-type">String</span> <span class="hljs-variable">rejectStep</span> <span class="hljs-operator">=</span> flowModel.getRejectStep();
    <span class="hljs-type">boolean</span> <span class="hljs-variable">rejectType</span> <span class="hljs-operator">=</span> FlowNature.PresentType.equals(flowModel.getRejectType());
    <span class="hljs-type">boolean</span> <span class="hljs-variable">isStart</span> <span class="hljs-operator">=</span> FlowNature.StartType.equals(flowModel.getRejectType());

    <span class="hljs-comment">// 创建驳回记录</span>
    <span class="hljs-type">FlowTaskOperatorRecordEntity</span> <span class="hljs-variable">operatorRecord</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">FlowTaskOperatorRecordEntity</span>();
    operatorRecord.setTaskId(flowTask.getId());
    operatorRecord.setHandleId(operator.getHandleId());
    operatorRecord.setHandleStatus(FlowRecordEnum.reject.getCode());
    operatorRecord.setHandleOpinion(flowModel.getHandleOpinion());
    operatorRecord.setHandleTime(<span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>());
    flowTaskOperatorRecordService.create(operatorRecord);

    <span class="hljs-comment">// 流程回退处理</span>
    <span class="hljs-keyword">if</span> (isStart) {
        <span class="hljs-comment">// 驳回到发起人</span>
        flowTask.setStatus(FlowTaskStatusEnum.Reject.getCode());
        flowTask.setCompletion(FlowNature.ProcessCompletion);
        flowTask.setThisStepId(String.join(<span class="hljs-string">&quot;,&quot;</span>, <span class="hljs-keyword">new</span> <span class="hljs-title class_">ArrayList</span>&lt;&gt;()));
        flowTask.setThisStep(String.join(<span class="hljs-string">&quot;,&quot;</span>, <span class="hljs-keyword">new</span> <span class="hljs-title class_">ArrayList</span>&lt;&gt;()));
    } <span class="hljs-keyword">else</span> <span class="hljs-keyword">if</span> (rejectType) {
        <span class="hljs-comment">// 驳回到上一节点</span>
        flowTask.setThisStepId(rejectStep);
    }

    flowTaskService.update(flowTask);
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<h4 id="1024-阶段四处置节点执行">10.2.4 阶段四：处置节点执行</h4>
<p><strong>触发条件</strong>：处置人员接收到待处置任务并进行处理</p>
<p><strong>处置流程</strong>：</p>
<p>&lt;augment_code_snippet path=&quot;aqsoc-monitor/src/main/java/com/ruoyi/work/service/impl/TblWorkOrderServiceImpl.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-comment">/**
 * 修改工单主表
 */</span>
<span class="hljs-meta">@Transactional(rollbackFor = Exception.class)</span>
<span class="hljs-meta">@Override</span>
<span class="hljs-keyword">public</span> <span class="hljs-type">int</span> <span class="hljs-title function_">updateTblWorkOrder</span><span class="hljs-params">(TblWorkOrder tblWorkOrder)</span> {
    <span class="hljs-type">Lock</span> <span class="hljs-variable">lock</span> <span class="hljs-operator">=</span> lockPool.computeIfAbsent(tblWorkOrder.getId() != <span class="hljs-literal">null</span>? tblWorkOrder.getId().toString() : tblWorkOrder.getProdId(), id -&gt; <span class="hljs-keyword">new</span> <span class="hljs-title class_">ReentrantLock</span>());
    lock.lock();
    <span class="hljs-keyword">try</span> {
        <span class="hljs-comment">// 工单状态为 待处理变更为待验证  待验证 根据验证是否通过将状态变更为 待处理 / 处理完成</span>
        <span class="hljs-type">TblWorkOrder</span> <span class="hljs-variable">workOrderInDB</span> <span class="hljs-operator">=</span> <span class="hljs-literal">null</span>;
        <span class="hljs-keyword">if</span>(tblWorkOrder.getId() != <span class="hljs-literal">null</span>){
            workOrderInDB = <span class="hljs-built_in">this</span>.selectTblWorkOrderById(tblWorkOrder.getId());
        }

        <span class="hljs-comment">// 更新工单基本信息</span>
        tblWorkOrder.setUpdateBy(getUsername());
        tblWorkOrder.setUpdateTime(DateUtils.getNowDate());

        <span class="hljs-type">int</span> <span class="hljs-variable">updateTblWorkOrder</span> <span class="hljs-operator">=</span> tblWorkOrderMapper.updateTblWorkOrder(tblWorkOrder);

        <span class="hljs-comment">//通报对象处理</span>
        List&lt;JSONObject&gt; reportTargetForm = tblWorkOrder.getReportTargetForm();
        handleReportTargetForm(reportTargetForm,tblWorkOrder);

        <span class="hljs-keyword">if</span>(isTempSave){
            <span class="hljs-keyword">return</span> updateTblWorkOrder;
        }

        <span class="hljs-comment">//通知</span>
        <span class="hljs-keyword">if</span>(flowNotify != <span class="hljs-literal">null</span> &amp;&amp; flowNotify.getBoolean(<span class="hljs-string">&quot;enabled&quot;</span>) != <span class="hljs-literal">null</span> &amp;&amp; flowNotify.getBoolean(<span class="hljs-string">&quot;enabled&quot;</span>) &amp;&amp; nodeIsFinished.get()){
            <span class="hljs-built_in">this</span>.sendFlowNotify(flowInfo, notifyList,tblWorkOrder);
        }

        <span class="hljs-comment">// 处理关联事件状态</span>
        <span class="hljs-keyword">if</span>(CollUtil.isNotEmpty(eventDataList)){
            <span class="hljs-comment">//按类型分类</span>
            <span class="hljs-keyword">for</span> (<span class="hljs-type">int</span> <span class="hljs-variable">i</span> <span class="hljs-operator">=</span> <span class="hljs-number">0</span>; i &lt; <span class="hljs-number">4</span>; i++) {
                <span class="hljs-comment">//获取事件</span>
                <span class="hljs-type">int</span> <span class="hljs-variable">finalI</span> <span class="hljs-operator">=</span> i;
                List&lt;JSONObject&gt; matchList = eventDataList.stream().filter(item -&gt; String.valueOf(finalI + <span class="hljs-number">1</span>).equals(item.getString(<span class="hljs-string">&quot;type&quot;</span>))).collect(Collectors.toList());
                <span class="hljs-keyword">if</span>(CollUtil.isNotEmpty(matchList)){
                    matchList.forEach(item -&gt; {
                        <span class="hljs-type">String</span> <span class="hljs-variable">eventId</span> <span class="hljs-operator">=</span> item.getString(<span class="hljs-string">&quot;eventId&quot;</span>);
                        <span class="hljs-type">String</span> <span class="hljs-variable">type</span> <span class="hljs-operator">=</span> item.getString(<span class="hljs-string">&quot;type&quot;</span>);
                        <span class="hljs-type">Integer</span> <span class="hljs-variable">handleState</span> <span class="hljs-operator">=</span> item.getInteger(<span class="hljs-string">&quot;handleState&quot;</span>);
                        <span class="hljs-type">String</span> <span class="hljs-variable">eventHandleState</span> <span class="hljs-operator">=</span> <span class="hljs-string">&quot;1&quot;</span>;
                        <span class="hljs-keyword">if</span>(handleState != <span class="hljs-literal">null</span> &amp;&amp; handleState == <span class="hljs-number">2</span>){
                            eventHandleState = <span class="hljs-string">&quot;2&quot;</span>;
                        }
                        handleEvent(CollUtil.toList(item),type,eventHandleState,tblWorkOrder);
                    });
                }
            }
        }

        <span class="hljs-keyword">return</span> updateTblWorkOrder;
    } <span class="hljs-keyword">finally</span> {
        lock.unlock();
    }
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<p><strong>事件处理逻辑</strong>：</p>
<p>&lt;augment_code_snippet path=&quot;aqsoc-monitor/src/main/java/com/ruoyi/work/service/impl/TblWorkOrderServiceImpl.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-keyword">private</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">handleEvent</span><span class="hljs-params">(List&lt;JSONObject&gt; eventDataList,String workType,String state,TblWorkOrder tblWorkOrder)</span>{
    <span class="hljs-type">LoginUser</span> <span class="hljs-variable">loginUser</span> <span class="hljs-operator">=</span> SecurityUtils.getLoginUser();
    SyncMessage&lt;Object&gt; syncMessage = <span class="hljs-keyword">new</span> <span class="hljs-title class_">SyncMessage</span>&lt;&gt;();
    syncMessage.setOperationType(OperationTypeEnum.INSERT);
    List&lt;Object&gt; syncDataList = <span class="hljs-keyword">new</span> <span class="hljs-title class_">ArrayList</span>&lt;&gt;();
    List&lt;Long&gt; eventList = eventDataList.stream().map(item -&gt; item.getLong(<span class="hljs-string">&quot;eventId&quot;</span>)).collect(Collectors.toList());

    <span class="hljs-keyword">if</span>(<span class="hljs-string">&quot;1&quot;</span>.equals(workType)){
        <span class="hljs-comment">//IP漏洞</span>
        List&lt;MonitorBssVulnDeal&gt; handleList = eventDataList.stream().map(item -&gt; {
            <span class="hljs-type">MonitorBssVulnDeal</span> <span class="hljs-variable">monitorBssVulnDeal</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">MonitorBssVulnDeal</span>();
            monitorBssVulnDeal.setId(item.getLong(<span class="hljs-string">&quot;eventId&quot;</span>));
            monitorBssVulnDeal.setHandleState(state);  <span class="hljs-comment">// 1:已处理 2:误报</span>
            monitorBssVulnDeal.setWorkOrderId(tblWorkOrder.getId());
            monitorBssVulnDeal.setHandleTime(DateUtil.date());
            monitorBssVulnDeal.setHandleUser(loginUser.getUsername());
            <span class="hljs-keyword">return</span> monitorBssVulnDeal;
        }).collect(Collectors.toList());
        monitorBssVulnDealService.updateBatchById(handleList);
        syncDataList.addAll(handleList);
    }<span class="hljs-keyword">else</span> <span class="hljs-keyword">if</span>(<span class="hljs-string">&quot;2&quot;</span>.equals(workType)){
        <span class="hljs-comment">//Web漏洞 - 类似处理逻辑</span>
    }<span class="hljs-keyword">else</span> <span class="hljs-keyword">if</span>(<span class="hljs-string">&quot;3&quot;</span>.equals(workType)){
        <span class="hljs-comment">//弱口令 - 类似处理逻辑</span>
    }<span class="hljs-keyword">else</span> <span class="hljs-keyword">if</span>(<span class="hljs-string">&quot;4&quot;</span>.equals(workType)){
        <span class="hljs-comment">//威胁事件 - 类似处理逻辑</span>
    }

    <span class="hljs-comment">// 同步数据到中台</span>
    <span class="hljs-keyword">if</span>(CollUtil.isNotEmpty(syncDataList)){
        syncDataList.forEach(syncDataItem -&gt; {
            syncMessage.setData(syncDataItem);
            syncMessage.setTimestamp(System.currentTimeMillis());
            handleDataSyncSender.sendDataSync(syncMessage);
        });
    }
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<p><strong>状态变化</strong>：</p>
<ul>
<li><strong>工单状态</strong>：<code>flow_state = &quot;3&quot;</code> (待核验)</li>
<li><strong>关联事件状态</strong>：更新为已处理或误报</li>
<li><strong>数据库操作</strong>：
<ul>
<li>更新 <code>tbl_work_order</code> 处置信息</li>
<li>更新关联事件表的 <code>handle_state</code></li>
<li>同步数据到中台系统</li>
</ul>
</li>
</ul>
<h4 id="1025-阶段五核验节点确认">10.2.5 阶段五：核验节点确认</h4>
<p><strong>触发条件</strong>：核验人员对处置结果进行验证</p>
<p><strong>核验通过流程</strong>：</p>
<p>&lt;augment_code_snippet path=&quot;jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/util/FlowTaskUtil.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-comment">/**
 * 流程完成处理
 */</span>
<span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">flowCompletion</span><span class="hljs-params">(FlowTaskEntity flowTask, FlowModel flowModel)</span> {
    <span class="hljs-comment">// 判断是否为最后节点</span>
    List&lt;FlowTaskNodeEntity&gt; taskNodeList = flowTaskNodeService.getList(flowTask.getId());
    <span class="hljs-type">boolean</span> <span class="hljs-variable">isLastNode</span> <span class="hljs-operator">=</span> taskNodeList.stream()
        .filter(t -&gt; FlowNature.AuditCompletion.equals(t.getCompletion()))
        .count() == taskNodeList.size() - <span class="hljs-number">1</span>; <span class="hljs-comment">// 排除开始节点</span>

    <span class="hljs-keyword">if</span> (isLastNode) {
        <span class="hljs-comment">// 流程完成</span>
        flowTask.setStatus(FlowTaskStatusEnum.Completion.getCode());
        flowTask.setCompletion(FlowNature.AuditCompletion);
        flowTask.setEndTime(<span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>());
        flowTaskService.update(flowTask);

        <span class="hljs-comment">// 触发流程完成事件</span>
        <span class="hljs-type">FlowMsgModel</span> <span class="hljs-variable">flowMsgModel</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">FlowMsgModel</span>();
        flowMsgModel.setTaskEntity(flowTask);
        flowMsgModel.setCompletion(<span class="hljs-literal">true</span>);
        flowMsgUtil.message(flowMsgModel);

        <span class="hljs-comment">// 更新工单状态为已完成</span>
        updateWorkOrderStatus(flowTask.getProcessId(), <span class="hljs-string">&quot;4&quot;</span>);
    }
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<p><strong>核验不通过流程</strong>：</p>
<p>&lt;augment_code_snippet path=&quot;aqsoc-monitor/src/main/java/com/ruoyi/work/service/impl/TblWorkOrderServiceImpl.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-comment">/**
 * 工单状态同步处理
 */</span>
<span class="hljs-keyword">private</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">syncWorkOrderStatus</span><span class="hljs-params">(String processId, String flowState)</span> {
    <span class="hljs-comment">// 根据流程状态映射工单状态</span>
    <span class="hljs-type">String</span> <span class="hljs-variable">workOrderState</span> <span class="hljs-operator">=</span> flowStateMap.get(flowState);
    <span class="hljs-keyword">if</span> (workOrderState != <span class="hljs-literal">null</span>) {
        <span class="hljs-type">TblWorkOrder</span> <span class="hljs-variable">updateOrder</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">TblWorkOrder</span>();
        updateOrder.setProdId(processId);
        updateOrder.setFlowState(flowState);
        updateOrder.setUpdateTime(DateUtils.getNowDate());

        <span class="hljs-comment">// 更新工单状态</span>
        tblWorkOrderMapper.updateByProcessId(updateOrder);

        <span class="hljs-comment">// 更新待办任务状态</span>
        <span class="hljs-keyword">if</span> (<span class="hljs-string">&quot;4&quot;</span>.equals(flowState)) {
            <span class="hljs-comment">// 已完成，清理待办</span>
            tblWorkBacklogService.deleteByProcessId(processId);
        } <span class="hljs-keyword">else</span> <span class="hljs-keyword">if</span> (<span class="hljs-string">&quot;1&quot;</span>.equals(flowState)) {
            <span class="hljs-comment">// 驳回到待处置，重新生成待办</span>
            generateBacklogTask(processId, <span class="hljs-string">&quot;disposal&quot;</span>);
        }

        <span class="hljs-comment">// 记录状态变更历史</span>
        <span class="hljs-type">TblWorkHistory</span> <span class="hljs-variable">history</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">TblWorkHistory</span>();
        history.setWorkOrderId(getWorkOrderIdByProcessId(processId));
        history.setOperationType(<span class="hljs-string">&quot;状态变更&quot;</span>);
        history.setOperationContent(<span class="hljs-string">&quot;工单状态变更为：&quot;</span> + workOrderState);
        history.setOperationTime(DateUtils.getNowDate());
        history.setOperationUser(getUsername());
        tblWorkHistoryService.insertTblWorkHistory(history);
    }
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<p><strong>状态变化</strong>：</p>
<ul>
<li><strong>核验通过</strong>：<code>flow_state = &quot;4&quot;</code> (已完成)</li>
<li><strong>核验不通过</strong>：<code>flow_state = &quot;1&quot;</code> (待处置)</li>
<li><strong>数据库操作</strong>：
<ul>
<li>更新 <code>tbl_work_order.flow_state</code></li>
<li>更新 <code>flow_task.status</code> 和 <code>end_time</code></li>
<li>清理 <code>tbl_work_backlog</code> 待办记录</li>
<li>插入 <code>tbl_work_history</code> 完成记录</li>
</ul>
</li>
</ul>
<h4 id="1026-特殊操作转办机制">10.2.6 特殊操作：转办机制</h4>
<p><strong>转办流程</strong>：</p>
<p>&lt;augment_code_snippet path=&quot;jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-meta">@Override</span>
<span class="hljs-meta">@DSTransactional</span>
<span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">transfer</span><span class="hljs-params">(FlowTaskOperatorEntity taskOperator, FlowModel flowModel)</span> <span class="hljs-keyword">throws</span> WorkFlowException {
    <span class="hljs-comment">// 更新经办人信息</span>
    taskOperator.setHandleId(flowModel.getFreeApproverUserId());
    taskOperator.setCreatorTime(<span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>());
    flowTaskOperatorService.update(taskOperator);

    <span class="hljs-comment">// 获取节点信息</span>
    List&lt;FlowTaskNodeEntity&gt; taskNodeList = flowTaskNodeService.getList(taskOperator.getTaskId());
    <span class="hljs-type">FlowTaskNodeEntity</span> <span class="hljs-variable">taskNode</span> <span class="hljs-operator">=</span> taskNodeList.stream()
        .filter(t -&gt; t.getId().equals(taskOperator.getTaskNodeId()))
        .findFirst().orElse(<span class="hljs-literal">null</span>);
    <span class="hljs-type">ChildNodeList</span> <span class="hljs-variable">childNode</span> <span class="hljs-operator">=</span> JsonUtil.getJsonToBean(taskNode.getNodePropertyJson(), ChildNodeList.class);

    <span class="hljs-comment">// 创建转办记录</span>
    <span class="hljs-type">UserInfo</span> <span class="hljs-variable">userInfo</span> <span class="hljs-operator">=</span> flowModel.getUserInfo();
    <span class="hljs-type">FlowTaskOperatorRecordEntity</span> <span class="hljs-variable">operatorRecord</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">FlowTaskOperatorRecordEntity</span>();
    <span class="hljs-type">FlowTaskOperatorEntity</span> <span class="hljs-variable">operator</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">FlowTaskOperatorEntity</span>();
    operator.setHandleId(userInfo.getUserId());
    operator.setTaskId(taskOperator.getTaskId());
    operator.setTaskNodeId(taskOperator.getTaskNodeId());
    operator.setNodeCode(taskOperator.getNodeCode());
    operator.setNodeName(taskOperator.getNodeName());

    <span class="hljs-type">FlowOperatordModel</span> <span class="hljs-variable">flowOperatordModel</span> <span class="hljs-operator">=</span> FlowOperatordModel.builder()
        .status(FlowRecordEnum.transfer.getCode())
        .flowModel(flowModel)
        .userId(userInfo.getUserId())
        .operator(operator)
        .operatorId(taskOperator.getHandleId())
        .build();
    flowTaskUtil.operatorRecord(operatorRecord, flowOperatordModel);
    flowTaskOperatorRecordService.create(operatorRecord);

    <span class="hljs-comment">// 发送转办消息通知</span>
    <span class="hljs-type">FlowMsgModel</span> <span class="hljs-variable">flowMsgModel</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">FlowMsgModel</span>();
    flowMsgModel.setTransfer(<span class="hljs-literal">true</span>);
    flowMsgModel.setTaskEntity(flowTaskService.getInfo(taskOperator.getTaskId()));
    flowMsgModel.setOperatorEntity(taskOperator);
    flowMsgUtil.message(flowMsgModel);
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<h4 id="1027-特殊操作撤回机制">10.2.7 特殊操作：撤回机制</h4>
<p><strong>撤回流程</strong>：</p>
<p>&lt;augment_code_snippet path=&quot;jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-meta">@Override</span>
<span class="hljs-meta">@DSTransactional</span>
<span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">recall</span><span class="hljs-params">(String id, FlowTaskOperatorRecordEntity operatorRecord, FlowModel flowModel)</span> <span class="hljs-keyword">throws</span> WorkFlowException {
    <span class="hljs-type">UserInfo</span> <span class="hljs-variable">userInfo</span> <span class="hljs-operator">=</span> flowModel.getUserInfo();

    <span class="hljs-comment">//撤回经办</span>
    <span class="hljs-type">FlowTaskOperatorEntity</span> <span class="hljs-variable">operatorEntity</span> <span class="hljs-operator">=</span> flowTaskOperatorService.getInfo(operatorRecord.getTaskOperatorId());
    <span class="hljs-keyword">if</span> (FlowNodeEnum.Futility.getCode().equals(operatorEntity.getState())) {
        <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">WorkFlowException</span>(MsgCode.WF104.get());
    }

    <span class="hljs-comment">// 获取流程信息</span>
    <span class="hljs-type">FlowTaskEntity</span> <span class="hljs-variable">flowTask</span> <span class="hljs-operator">=</span> flowTaskService.getInfo(operatorRecord.getTaskId());
    List&lt;FlowTaskNodeEntity&gt; flowTaskNodeEntityList = flowTaskNodeService.getList(operatorRecord.getTaskId());
    List&lt;FlowTaskOperatorEntity&gt; flowTaskOperatorEntityList = flowTaskOperatorService.getList(operatorRecord.getTaskId());

    <span class="hljs-type">FlowTaskNodeEntity</span> <span class="hljs-variable">flowTaskNodeEntity</span> <span class="hljs-operator">=</span> flowTaskNodeEntityList.stream()
        .filter(t -&gt; t.getId().equals(operatorRecord.getTaskNodeId()))
        .findFirst().orElse(<span class="hljs-literal">null</span>);

    <span class="hljs-comment">//任务待审状态才能撤回</span>
    <span class="hljs-keyword">if</span> (!(flowTask.getEnabledMark() == <span class="hljs-number">1</span> &amp;&amp; FlowTaskStatusEnum.Handle.getCode().equals(flowTask.getStatus()))) {
        <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">WorkFlowException</span>(MsgCode.WF105.get());
    }

    <span class="hljs-comment">//撤回节点下一节点已操作检查</span>
    List&lt;FlowTaskOperatorEntity&gt; recallNextOperatorList = flowTaskOperatorEntityList.stream()
        .filter(x -&gt; flowTaskNodeEntity.getNodeNext().contains(x.getNodeCode()))
        .collect(Collectors.toList());
    <span class="hljs-type">boolean</span> <span class="hljs-variable">isRecall</span> <span class="hljs-operator">=</span> recallNextOperatorList.stream()
        .filter(t -&gt; FlowNature.AuditCompletion.equals(t.getCompletion()) &amp;&amp; FlowNodeEnum.Process.getCode().equals(t.getState()))
        .count() &gt; <span class="hljs-number">0</span>;
    <span class="hljs-keyword">if</span> (isRecall) {
        <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">WorkFlowException</span>(MsgCode.WF106.get());
    }

    <span class="hljs-comment">// 撤回节点是否完成</span>
    <span class="hljs-keyword">if</span> (FlowNature.AuditCompletion.equals(flowTaskNodeEntity.getCompletion())) {
        <span class="hljs-comment">//撤回节点下一节点经办删除</span>
        List&lt;String&gt; idAll = recallNextOperatorList.stream()
            .map(FlowTaskOperatorEntity::getId)
            .collect(Collectors.toList());
        flowTaskOperatorService.updateTaskOperatorState(idAll);

        <span class="hljs-comment">// 重置当前节点经办人状态</span>
        List&lt;FlowTaskOperatorEntity&gt; hanleOperatorList = flowTaskOperatorEntityList.stream()
            .filter(x -&gt; x.getTaskNodeId().equals(operatorRecord.getTaskNodeId())
                &amp;&amp; Objects.isNull(x.getHandleStatus())
                &amp;&amp; Objects.isNull(x.getHandleTime())
                &amp;&amp; Objects.isNull(x.getParentId()))
            .collect(Collectors.toList());
        <span class="hljs-keyword">for</span> (FlowTaskOperatorEntity taskOperator : hanleOperatorList) {
            taskOperator.setCompletion(FlowNature.ProcessCompletion);
        }
        flowTaskOperatorService.updateList(hanleOperatorList);
    }

    <span class="hljs-comment">// 创建撤回记录</span>
    <span class="hljs-type">FlowTaskOperatorRecordEntity</span> <span class="hljs-variable">revokeRecord</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">FlowTaskOperatorRecordEntity</span>();
    revokeRecord.setTaskId(operatorRecord.getTaskId());
    revokeRecord.setHandleId(userInfo.getUserId());
    revokeRecord.setHandleStatus(FlowRecordEnum.recall.getCode());
    revokeRecord.setHandleOpinion(flowModel.getHandleOpinion());
    revokeRecord.setHandleTime(<span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>());
    flowTaskOperatorRecordService.create(revokeRecord);
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<p><strong>发起撤回（revoke）流程</strong>：</p>
<p>&lt;augment_code_snippet path=&quot;jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java&quot; mode=&quot;EXCERPT&quot;&gt;</p>
<pre><code class="language-java"><span class="hljs-meta">@Override</span>
<span class="hljs-meta">@DSTransactional</span>
<span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">revoke</span><span class="hljs-params">(FlowTaskEntity flowTask, FlowModel flowModel)</span> <span class="hljs-keyword">throws</span> WorkFlowException {
    <span class="hljs-comment">//判断流程是否处于挂起状态</span>
    flowTaskUtil.isSuspend(flowTask);
    <span class="hljs-type">UserInfo</span> <span class="hljs-variable">userInfo</span> <span class="hljs-operator">=</span> flowModel.getUserInfo();

    List&lt;FlowTaskNodeEntity&gt; list = flowTaskNodeService.getList(flowTask.getId());
    <span class="hljs-type">FlowTaskNodeEntity</span> <span class="hljs-variable">start</span> <span class="hljs-operator">=</span> list.stream()
        .filter(t -&gt; FlowNature.NodeStart.equals(String.valueOf(t.getNodeType())))
        .findFirst().orElse(<span class="hljs-literal">null</span>);

    <span class="hljs-comment">//删除节点</span>
    flowTaskNodeService.update(flowTask.getId());
    <span class="hljs-comment">//删除经办</span>
    flowTaskOperatorService.update(flowTask.getId());
    <span class="hljs-comment">//删除候选人</span>
    flowCandidatesService.deleteByTaskId(flowTask.getId());
    <span class="hljs-comment">//删除发起用户信息</span>
    flowUserService.deleteByTaskId(flowTask.getId());

    <span class="hljs-comment">//更新当前节点</span>
    flowTask.setThisStep(<span class="hljs-string">&quot;开始&quot;</span>);
    flowTask.setCompletion(FlowNature.ProcessCompletion);
    flowTask.setStatus(FlowTaskStatusEnum.Revoke.getCode());
    flowTask.setThisStepId(start.getNodeCode());
    flowTaskService.update(flowTask);

    <span class="hljs-comment">// 创建撤回记录</span>
    <span class="hljs-type">FlowTaskOperatorRecordEntity</span> <span class="hljs-variable">operatorRecord</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">FlowTaskOperatorRecordEntity</span>();
    operatorRecord.setTaskId(flowTask.getId());
    operatorRecord.setHandleId(userInfo.getUserId());
    operatorRecord.setHandleStatus(FlowRecordEnum.revoke.getCode());
    operatorRecord.setHandleOpinion(<span class="hljs-string">&quot;发起撤回&quot;</span>);
    operatorRecord.setHandleTime(<span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>());
    flowTaskOperatorRecordService.create(operatorRecord);
}
</code></pre>
<p>&lt;/augment_code_snippet&gt;</p>
<h3 id="103-状态同步机制">10.3 状态同步机制</h3>
<h4 id="1031-状态映射关系">10.3.1 状态映射关系</h4>
<pre><code class="language-java"><span class="hljs-comment">// TblWorkOrderServiceImpl中的状态映射</span>
<span class="hljs-keyword">private</span> <span class="hljs-keyword">static</span> <span class="hljs-keyword">final</span> Map&lt;String,String&gt; flowStateMap = <span class="hljs-keyword">new</span> <span class="hljs-title class_">HashMap</span>&lt;&gt;();
<span class="hljs-keyword">static</span> {
    flowStateMap.put(<span class="hljs-string">&quot;-1&quot;</span>, <span class="hljs-string">&quot;待发起&quot;</span>);   <span class="hljs-comment">// 草稿或撤回状态</span>
    flowStateMap.put(<span class="hljs-string">&quot;0&quot;</span>, <span class="hljs-string">&quot;待审核&quot;</span>);    <span class="hljs-comment">// 审核节点</span>
    flowStateMap.put(<span class="hljs-string">&quot;2&quot;</span>, <span class="hljs-string">&quot;待审核&quot;</span>);    <span class="hljs-comment">// 审核节点（兼容）</span>
    flowStateMap.put(<span class="hljs-string">&quot;1&quot;</span>, <span class="hljs-string">&quot;待处置&quot;</span>);    <span class="hljs-comment">// 处置节点</span>
    flowStateMap.put(<span class="hljs-string">&quot;3&quot;</span>, <span class="hljs-string">&quot;待核验&quot;</span>);    <span class="hljs-comment">// 核验节点</span>
    flowStateMap.put(<span class="hljs-string">&quot;4&quot;</span>, <span class="hljs-string">&quot;已完成&quot;</span>);    <span class="hljs-comment">// 流程完成</span>
}
</code></pre>
<h4 id="1032-同步时机">10.3.2 同步时机</h4>
<ol>
<li><strong>流程提交时</strong>：工单状态从 <code>-1</code> 更新为 <code>2</code></li>
<li><strong>审核通过时</strong>：工单状态从 <code>2</code> 更新为 <code>1</code></li>
<li><strong>处置完成时</strong>：工单状态从 <code>1</code> 更新为 <code>3</code></li>
<li><strong>核验通过时</strong>：工单状态从 <code>3</code> 更新为 <code>4</code></li>
<li><strong>驳回操作时</strong>：根据驳回目标节点更新状态</li>
<li><strong>撤回操作时</strong>：工单状态更新为 <code>-1</code></li>
</ol>
<h3 id="104-通知机制">10.4 通知机制</h3>
<h4 id="1041-流程通知">10.4.1 流程通知</h4>
<pre><code class="language-java"><span class="hljs-comment">// 流程通知处理</span>
<span class="hljs-keyword">private</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">sendFlowNotify</span><span class="hljs-params">(JSONObject flowInfo, List&lt;TblWorkBacklog&gt; notifyList, TblWorkOrder tblWorkOrder)</span> {
    <span class="hljs-type">JSONObject</span> <span class="hljs-variable">flowNotify</span> <span class="hljs-operator">=</span> flowInfo.getJSONObject(<span class="hljs-string">&quot;flowNotify&quot;</span>);
    <span class="hljs-keyword">if</span> (flowNotify != <span class="hljs-literal">null</span> &amp;&amp; flowNotify.getBoolean(<span class="hljs-string">&quot;enabled&quot;</span>)) {
        <span class="hljs-comment">// 生成系统通知</span>
        <span class="hljs-keyword">for</span> (TblWorkBacklog backlog : notifyList) {
            <span class="hljs-type">SysNotice</span> <span class="hljs-variable">notice</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">SysNotice</span>();
            notice.setNoticeTitle(<span class="hljs-string">&quot;威胁通报处理通知&quot;</span>);
            notice.setNoticeContent(<span class="hljs-string">&quot;您有新的威胁通报需要处理：&quot;</span> + tblWorkOrder.getWorkName());
            notice.setNoticeType(<span class="hljs-string">&quot;1&quot;</span>);
            notice.setStatus(<span class="hljs-string">&quot;0&quot;</span>);
            notice.setCreateBy(tblWorkOrder.getCreateBy());
            notice.setCreateTime(<span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>());
            sysNoticeService.insertNotice(notice);
        }

        <span class="hljs-comment">// 发送短信通知（如果配置启用）</span>
        <span class="hljs-keyword">if</span> (flowNotify.getBoolean(<span class="hljs-string">&quot;smsEnabled&quot;</span>)) {
            sendSmsNotification(notifyList, tblWorkOrder);
        }
    }
}
</code></pre>
<h4 id="1042-抄送通知">10.4.2 抄送通知</h4>
<pre><code class="language-java"><span class="hljs-comment">// 抄送人通知处理</span>
<span class="hljs-type">FlowMsgModel</span> <span class="hljs-variable">flowMsgModel</span> <span class="hljs-operator">=</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">FlowMsgModel</span>();
flowMsgModel.setCopy(<span class="hljs-literal">true</span>);  <span class="hljs-comment">// 标记为抄送消息</span>
flowMsgModel.setCirculateList(circulateList);
flowMsgModel.setTaskEntity(flowTask);
flowMsgUtil.message(flowMsgModel);
</code></pre>
<h3 id="105-数据一致性保障">10.5 数据一致性保障</h3>
<h4 id="1051-事务管理">10.5.1 事务管理</h4>
<pre><code class="language-java"><span class="hljs-meta">@Transactional(rollbackFor = Exception.class)</span>
<span class="hljs-keyword">public</span> <span class="hljs-type">int</span> <span class="hljs-title function_">updateTblWorkOrder</span><span class="hljs-params">(TblWorkOrder tblWorkOrder)</span> {
    <span class="hljs-comment">// 使用分布式锁保证数据一致性</span>
    <span class="hljs-type">Lock</span> <span class="hljs-variable">lock</span> <span class="hljs-operator">=</span> lockPool.computeIfAbsent(tblWorkOrder.getId().toString(), id -&gt; <span class="hljs-keyword">new</span> <span class="hljs-title class_">ReentrantLock</span>());
    lock.lock();
    <span class="hljs-keyword">try</span> {
        <span class="hljs-comment">// 业务处理逻辑</span>
        <span class="hljs-keyword">return</span> processWorkOrder(tblWorkOrder);
    } <span class="hljs-keyword">finally</span> {
        lock.unlock();
    }
}
</code></pre>
<h4 id="1052-状态校验">10.5.2 状态校验</h4>
<pre><code class="language-java"><span class="hljs-comment">// 状态流转校验</span>
<span class="hljs-keyword">private</span> <span class="hljs-keyword">void</span> <span class="hljs-title function_">validateStateTransition</span><span class="hljs-params">(String currentState, String targetState)</span> {
    Map&lt;String, List&lt;String&gt;&gt; allowedTransitions = <span class="hljs-keyword">new</span> <span class="hljs-title class_">HashMap</span>&lt;&gt;();
    allowedTransitions.put(<span class="hljs-string">&quot;-1&quot;</span>, Arrays.asList(<span class="hljs-string">&quot;2&quot;</span>));           <span class="hljs-comment">// 待发起 -&gt; 待审核</span>
    allowedTransitions.put(<span class="hljs-string">&quot;2&quot;</span>, Arrays.asList(<span class="hljs-string">&quot;1&quot;</span>, <span class="hljs-string">&quot;-1&quot;</span>));      <span class="hljs-comment">// 待审核 -&gt; 待处置/待发起</span>
    allowedTransitions.put(<span class="hljs-string">&quot;1&quot;</span>, Arrays.asList(<span class="hljs-string">&quot;3&quot;</span>, <span class="hljs-string">&quot;2&quot;</span>));       <span class="hljs-comment">// 待处置 -&gt; 待核验/待审核</span>
    allowedTransitions.put(<span class="hljs-string">&quot;3&quot;</span>, Arrays.asList(<span class="hljs-string">&quot;4&quot;</span>, <span class="hljs-string">&quot;1&quot;</span>));       <span class="hljs-comment">// 待核验 -&gt; 已完成/待处置</span>

    <span class="hljs-keyword">if</span> (!allowedTransitions.get(currentState).contains(targetState)) {
        <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-title class_">BusinessException</span>(<span class="hljs-string">&quot;非法的状态流转：&quot;</span> + currentState + <span class="hljs-string">&quot; -&gt; &quot;</span> + targetState);
    }
}
</code></pre>
<h2 id="11-总结">11. 总结</h2>
<h3 id="111-技术总结">11.1 技术总结</h3>
<p>aqsoc-main项目的威胁通报功能通过smiley-http-proxy-servlet实现了与aq-jnpf工作流引擎的集成，采用了微服务架构和数据分离的设计思路。整体架构清晰，功能完整，能够满足当前的业务需求。</p>
<h3 id="112-优势分析">11.2 优势分析</h3>
<ol>
<li><strong>架构清晰</strong>：前后端分离，业务逻辑与工作流引擎分离</li>
<li><strong>功能完整</strong>：涵盖了威胁通报的完整业务流程</li>
<li><strong>扩展性好</strong>：通过代理机制实现了系统间的松耦合</li>
<li><strong>技术成熟</strong>：使用了成熟稳定的技术栈</li>
</ol>
<h3 id="113-改进方向">11.3 改进方向</h3>
<ol>
<li><strong>性能优化</strong>：通过缓存、索引等手段提升系统性能</li>
<li><strong>可靠性提升</strong>：增强系统的容错能力和监控能力</li>
<li><strong>架构演进</strong>：向更现代的微服务和云原生架构演进</li>
<li><strong>技术升级</strong>：持续跟进新技术，提升开发效率</li>
</ol>
<p>通过本次深度分析，我们全面了解了威胁通报功能的实现机制和完整生命周期，为后续的系统优化和功能扩展提供了重要的技术参考。</p>

            <script async src="https://cdn.jsdelivr.net/npm/katex-copytex@latest/dist/katex-copytex.min.js"></script>
            
        </body>
        </html>